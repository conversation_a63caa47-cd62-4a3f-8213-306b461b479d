// ============================================================================
// ULTIMATE DASHBOARD SCREEN - Main Dashboard Selector
// ============================================================================
// This screen serves as the main dashboard hub where users can:
// 1. View their profile and health statistics
// 2. Choose between different dashboard styles (Modern/Enhanced)
// 3. See health overview and prediction history
// 4. Access account settings and profile management
//
// PREDICTION SYSTEM TYPES SUPPORTED:
// ----------------------------------
// • ML-BASED PREDICTIONS: Machine Learning algorithms that analyze symptoms
//   and risk factors to provide HIV risk assessment
// • RULE-BASED PREDICTIONS: Traditional logic-based system using predefined
//   rules and thresholds for risk calculation
//
// Both prediction types are stored in the same data structure and processed
// uniformly throughout the dashboard for consistent user experience.
//
// COLOR CODING SYSTEM:
// -------------------
// • GREEN (#00C851): Low risk - Good health status
// • AMBER (#FFB300): Medium risk - Caution advised
// • RED (#FF3547): High risk - Urgent attention needed
// ============================================================================

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

// Firebase Services - Handle authentication and database operations
import '../services/firebase_auth_service.dart';
import '../services/firebase_database_service.dart';

// Data Models - Structure for user data and prediction results
import '../models/prediction_result.dart';
import '../models/user.dart';

// UI Components and Screens
import '../widgets/health_chart_widget.dart'; // Health visualization charts
import 'enhanced_dashboard_screen.dart'; // Advanced dashboard with charts
import 'modern_dashboard_screen.dart'; // Clean, simple dashboard
import 'home_screen.dart'; // Assessment/prediction screen
import 'login_screen.dart'; // User authentication
import 'account_settings_screen.dart'; // Profile and settings management

// ============================================================================
// MAIN DASHBOARD WIDGET CLASS
// ============================================================================
class UltimateDashboardScreen extends StatefulWidget {
  const UltimateDashboardScreen({super.key});

  @override
  State<UltimateDashboardScreen> createState() =>
      _UltimateDashboardScreenState();
}

// ============================================================================
// DASHBOARD STATE MANAGEMENT
// ============================================================================
class _UltimateDashboardScreenState extends State<UltimateDashboardScreen>
    with TickerProviderStateMixin {
  // ========================================
  // SERVICE INSTANCES
  // ========================================
  late FirebaseAuthService _authService; // Handles user authentication
  late FirebaseDatabaseService
  _databaseService; // Manages Firebase database operations

  // ========================================
  // STATE VARIABLES
  // ========================================
  User? _currentUser; // Current logged-in user data
  List<PredictionResult> _recentPredictions =
      []; // User's prediction history (ML + Rule-based)
  bool _isLoading = true; // Loading state for UI
  int _selectedDashboard =
      0; // Dashboard selection (0=selector, 1=modern, 2=enhanced)

  // ========================================
  // ANIMATION CONTROLLERS
  // ========================================
  late AnimationController _animationController; // Controls fade-in animations
  late Animation<double> _fadeAnimation; // Fade transition animation

  // ========================================
  // INITIALIZATION METHODS
  // ========================================
  @override
  void initState() {
    super.initState();
    // Initialize Firebase services from Provider
    _authService = Provider.of<FirebaseAuthService>(context, listen: false);
    _databaseService = Provider.of<FirebaseDatabaseService>(
      context,
      listen: false,
    );
    // Setup UI animations and load user data
    _initializeAnimations();
    _loadUserData();
  }

  /// Initialize fade-in animations for smooth UI transitions
  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800), // 800ms fade duration
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward(); // Start animation immediately
  }

  @override
  void dispose() {
    _animationController.dispose(); // Clean up animation controller
    super.dispose();
  }

  // ========================================
  // DATA LOADING METHODS
  // ========================================

  /// Load user data and prediction history from Firebase
  /// This includes both ML-based and Rule-based prediction results
  Future<void> _loadUserData() async {
    try {
      // Get current authenticated user
      final user = await _authService.getCurrentUser();

      if (user != null) {
        // Fetch user's prediction history (contains both ML and Rule-based results)
        final predictions = await _databaseService.getUserPredictions();

        if (mounted) {
          setState(() {
            _currentUser = user;
            _recentPredictions =
                predictions; // All prediction types stored here
            _isLoading = false;
          });
        }
      } else {
        // User not authenticated - redirect to login
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const LoginScreen()),
          );
        }
      }
    } catch (e) {
      // Handle loading errors gracefully
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // ========================================
  // NAVIGATION METHODS
  // ========================================

  /// Navigate to profile editing screen
  void _showEditProfileDialog() {
    if (_currentUser == null) return;

    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AccountSettingsScreen()),
    );
  }

  // ========================================
  // MAIN UI BUILD METHOD
  // ========================================
  @override
  Widget build(BuildContext context) {
    // ========================================
    // LOADING STATE UI
    // ========================================
    if (_isLoading) {
      return Scaffold(
        body: Container(
          // Background: Blue gradient for loading screen
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF1976D2), Color(0xFF42A5F5)], // Blue gradient
            ),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  color: Colors.white,
                ), // Loading spinner
                SizedBox(height: 16),
                Text(
                  'Loading your personalized dashboard...',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // ========================================
    // AUTHENTICATION CHECK
    // ========================================
    if (_currentUser == null) {
      return const LoginScreen(); // Redirect to login if not authenticated
    }

    // ========================================
    // MAIN DASHBOARD SCAFFOLD
    // ========================================
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA), // Light gray background
      appBar: AppBar(
        title: const Text(
          'Health Dashboard',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: const Color(0xFF1976D2), // Primary blue color
        elevation: 0,
        actions: [
          // ========================================
          // APP BAR ACTION BUTTONS
          // ========================================

          // Edit Profile Button
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.white),
            onPressed: _showEditProfileDialog,
            tooltip: 'Edit Profile',
          ),

          // Account Settings Button
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AccountSettingsScreen(),
                ),
              );
            },
            tooltip: 'Account Settings',
          ),

          // Refresh Data Button - Reloads user predictions (ML + Rule-based)
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadUserData(); // Reload all prediction data
            },
            tooltip: 'Refresh Dashboard',
          ),

          // Debug Info Button - Shows prediction count
          IconButton(
            icon: const Icon(Icons.info_outline, color: Colors.white),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Dashboard has ${_recentPredictions.length} predictions loaded',
                  ),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            tooltip: 'Debug Info',
          ),
          // Dashboard Selector Menu - Choose between different dashboard styles
          PopupMenuButton<int>(
            icon: const Icon(Icons.dashboard, color: Colors.white),
            onSelected: (value) {
              setState(() {
                _selectedDashboard = value; // Update selected dashboard type
              });
            },
            itemBuilder: (context) => [
              // Dashboard Selector Option (default view)
              const PopupMenuItem(
                value: 0,
                child: Row(
                  children: [
                    Icon(Icons.dashboard_outlined, color: Color(0xFF1976D2)),
                    SizedBox(width: 8),
                    Text('Dashboard Selector'),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              // Modern Dashboard Option - Simple, clean interface
              const PopupMenuItem(
                value: 1,
                child: Row(
                  children: [
                    Icon(Icons.auto_awesome, color: Color(0xFF4CAF50)),
                    SizedBox(width: 8),
                    Text('Modern Dashboard'),
                  ],
                ),
              ),
              // Enhanced Dashboard Option - Advanced features with charts
              const PopupMenuItem(
                value: 2,
                child: Row(
                  children: [
                    Icon(Icons.rocket_launch, color: Color(0xFFFF9800)),
                    SizedBox(width: 8),
                    Text('Enhanced Dashboard'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      // ========================================
      // MAIN BODY WITH FADE ANIMATION
      // ========================================
      body: FadeTransition(
        opacity: _fadeAnimation, // Smooth fade-in animation
        child: _buildSelectedDashboard(), // Build the selected dashboard view
      ),
    );
  }

  // ========================================
  // DASHBOARD SELECTION LOGIC
  // ========================================

  /// Build the selected dashboard based on user choice
  Widget _buildSelectedDashboard() {
    switch (_selectedDashboard) {
      case 1:
        return const ModernDashboardScreen(); // Simple, clean dashboard
      case 2:
        return const EnhancedDashboardScreen(); // Advanced dashboard with charts
      default:
        return _buildDashboardSelector(); // Default selector view
    }
  }

  // ========================================
  // DASHBOARD SELECTOR UI
  // ========================================

  /// Build the main dashboard selector interface
  Widget _buildDashboardSelector() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20), // Consistent padding around content
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome card with user info and stats
          _buildWelcomeCard(),
          const SizedBox(height: 24),

          // ========================================
          // SECTION TITLE
          // ========================================
          const Text(
            'Choose Your Dashboard Style - Updated!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50), // Dark blue-gray title color
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select the dashboard that best fits your needs and preferences',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ), // Subtitle in gray
          ),
          const SizedBox(height: 24),
          // ========================================
          // DASHBOARD OPTIONS
          // ========================================

          // Modern Dashboard Option - Simple interface for basic health tracking
          _buildDashboardOption(
            title: 'Modern Dashboard',
            subtitle: 'Clean, intuitive design with essential features',
            icon: Icons.auto_awesome,
            color: const Color(0xFF4CAF50), // Green color for modern option
            features: [
              'Health status overview',
              'Quick action buttons',
              'Recent activity timeline',
              'Health insights & tips',
              'Smooth animations',
            ],
            onTap: () {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const ModernDashboardScreen(),
                ),
              );
            },
          ),
          const SizedBox(height: 16),

          // Enhanced Dashboard Option - Advanced interface with charts and analytics
          _buildDashboardOption(
            title: 'Enhanced Dashboard',
            subtitle: 'Advanced features with comprehensive health tracking',
            icon: Icons.rocket_launch,
            color: const Color(0xFFFF9800), // Orange color for enhanced option
            features: [
              'Interactive health charts', // Visual data representation
              'Health streak tracking', // Consistency monitoring
              'Motivational quotes', // User engagement
              'Advanced statistics', // Detailed analytics
              'Smart notifications', // Intelligent alerts
              'Profile management', // User account features
            ],
            onTap: () {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const EnhancedDashboardScreen(),
                ),
              );
            },
          ),
          const SizedBox(height: 24),
          _buildQuickStatsPreview(),
        ],
      ),
    );
  }

  // ========================================
  // WELCOME CARD UI COMPONENT
  // ========================================

  /// Build the welcome card with user info and health statistics
  /// Features blue gradient background and user avatar
  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24), // Internal padding for content
      decoration: BoxDecoration(
        // ========================================
        // BACKGROUND: Blue gradient design
        // ========================================
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF1976D2), Color(0xFF42A5F5)], // Dark to light blue
        ),
        borderRadius: BorderRadius.circular(20), // Rounded corners
        boxShadow: [
          // Drop shadow for depth effect
          BoxShadow(
            color: const Color(
              0xFF1976D2,
            ).withValues(alpha: 0.3), // Blue shadow
            blurRadius: 20,
            offset: const Offset(0, 10), // Shadow positioned below card
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.white.withValues(alpha: 0.2),
                child: Text(
                  _currentUser!.fullName.isNotEmpty
                      ? _currentUser!.fullName[0].toUpperCase()
                      : _currentUser!.username[0].toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Welcome back, ${_currentUser!.fullName.isNotEmpty ? _currentUser!.fullName.split(' ').first : _currentUser!.username}!',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: _showEditProfileDialog,
                          icon: const Icon(
                            Icons.edit,
                            color: Colors.white,
                            size: 20,
                          ),
                          tooltip: 'Edit Profile',
                          padding: const EdgeInsets.all(4),
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      'Ready to continue your health journey?',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildWelcomeStatItem(
                  'Assessments',
                  '${_recentPredictions.length}',
                  Icons.assessment,
                ),
              ),
              Expanded(
                child: _buildWelcomeStatItem(
                  'Health Score',
                  '${_getHealthScore()}%',
                  Icons.favorite,
                ),
              ),
              Expanded(
                child: _buildWelcomeStatItem(
                  'Streak',
                  '${_getHealthStreak()}d',
                  Icons.local_fire_department,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeStatItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required List<String> features,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(color: color.withValues(alpha: 0.1), width: 2),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [color, color.withValues(alpha: 0.8)],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: Colors.white, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2C3E50),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                Icon(Icons.arrow_forward_ios, color: color, size: 16),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: features.map((feature) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    feature,
                    style: TextStyle(
                      fontSize: 12,
                      color: color,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatsPreview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Your Health Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 16),
        if (_recentPredictions.isNotEmpty) ...[
          HealthChartWidget(
            predictions: _recentPredictions,
            title: 'Health Trend (Last 7 Days)',
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: HealthProgressWidget(
                  progress: _getHealthScore() / 100,
                  label: 'Overall Health',
                  color: _getHealthScoreColor(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: HealthProgressWidget(
                  progress: _getConsistencyScore(),
                  label: 'Consistency',
                  color: const Color(0xFF4CAF50),
                ),
              ),
            ],
          ),
        ] else
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(Icons.insights, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                const Text(
                  'Start Your Health Journey',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Take your first assessment to unlock personalized insights',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const HomeScreen(),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1976D2),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Start Assessment'),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // ========================================
  // HEALTH CALCULATION METHODS
  // ========================================
  // These methods process prediction data from both ML and Rule-based systems

  /// Calculate health score based on most recent prediction
  /// Works with both ML-based and Rule-based prediction results
  int _getHealthScore() {
    if (_recentPredictions.isEmpty) {
      return 85; // Default score when no predictions
    }

    // Get the most recent prediction (could be ML or Rule-based)
    final recentScore = _recentPredictions.first.riskLevel.toLowerCase();

    // Risk level mapping (applies to both ML and Rule-based results):
    if (recentScore.contains('low')) return 90; // Low risk = High health score
    if (recentScore.contains('medium')) {
      return 70; // Medium risk = Medium health score
    }
    return 45; // High risk = Low health score
  }

  /// Get color based on health score for visual risk indication
  /// Color coding: Green (Low risk), Amber (Medium risk), Red (High risk)
  Color _getHealthScoreColor() {
    final score = _getHealthScore();
    if (score >= 80) {
      return const Color(
        0xFF00C851,
      ); // Vibrant green for low risk (good health)
    }
    if (score >= 60) {
      return const Color(0xFFFFB300); // Amber for medium risk (caution)
    }
    return const Color(
      0xFFFF3547,
    ); // Bright red for high risk (urgent attention)
  }

  /// Calculate consistency score based on prediction frequency
  /// Measures how regularly user performs health assessments
  double _getConsistencyScore() {
    if (_recentPredictions.isEmpty) return 0.0;

    final streak = _getHealthStreak();
    return (streak / 30).clamp(0.0, 1.0); // Max 30 days for 100% consistency
  }

  /// Calculate health assessment streak (consecutive days with predictions)
  /// Counts both ML and Rule-based predictions for streak calculation
  int _getHealthStreak() {
    if (_recentPredictions.isEmpty) return 0;

    final now = DateTime.now();
    int streak = 0;

    // Count consecutive days with any type of prediction
    for (final prediction in _recentPredictions) {
      final daysDiff = now.difference(prediction.createdAt).inDays;
      if (daysDiff <= streak + 1) {
        streak++; // Increment streak for consecutive assessment days
      } else {
        break; // Break streak if gap found
      }
    }

    return streak;
  }
}
