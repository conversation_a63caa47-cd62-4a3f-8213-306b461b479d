import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hiv_predictor_app/main.dart';

void main() {
  testWidgets('App smoke test', (WidgetTester tester) async {
    // Test the main app
    await tester.pumpWidget(const MyApp());

    // Wait for the widget to render
    await tester.pump();

    // Check if the app loads without crashing
    expect(find.byType(MaterialApp), findsOneWidget);

    // Check if we can find basic UI elements (login form)
    expect(find.byType(TextFormField), findsWidgets);
  });
}
