import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/language_service.dart';

class BeautifulEducationScreen extends StatefulWidget {
  const BeautifulEducationScreen({super.key});

  @override
  State<BeautifulEducationScreen> createState() =>
      _BeautifulEducationScreenState();
}

class _BeautifulEducationScreenState extends State<BeautifulEducationScreen>
    with TickerProviderStateMixin {
  final LanguageService _languageService = LanguageService();
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  String _selectedLanguage = 'en';
  String _searchQuery = '';
  int _selectedCategoryIndex = 0;

  final List<String> _categories = [
    'all',
    'prevention',
    'symptoms',
    'testing',
    'treatment',
    'support',
    'myths_facts',
  ];

  @override
  void initState() {
    super.initState();
    _loadLanguage();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  void _loadLanguage() {
    // Always use English - no async needed
    _selectedLanguage = 'en';
  }

  @override
  void dispose() {
    _searchController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final texts = _languageService.getTexts(_selectedLanguage);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF667eea), Color(0xFF764ba2), Color(0xFFf093fb)],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                _buildHeader(texts),
                _buildSearchBar(texts),
                _buildCategoryTabs(texts),
                Expanded(child: _buildEducationContent(texts)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(Map<String, String> texts) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: Colors.white, size: 28),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  texts['hiv_education'] ?? 'HIV Education',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Complete guide from basics to advanced topics',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          // Language selector removed - English only
        ],
      ),
    );
  }

  Widget _buildSearchBar(Map<String, String> texts) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        decoration: InputDecoration(
          hintText: texts['search_topics'] ?? 'Search topics...',
          prefixIcon: const Icon(Icons.search, color: Color(0xFF667eea)),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 15,
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryTabs(Map<String, String> texts) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = _selectedCategoryIndex == index;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedCategoryIndex = index;
              });
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.white
                    : Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
              ),
              child: Text(
                texts[category] ?? category.replaceAll('_', ' ').toUpperCase(),
                style: TextStyle(
                  color: isSelected ? const Color(0xFF667eea) : Colors.white,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 14,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEducationContent(Map<String, String> texts) {
    final topics = _getFilteredTopics();

    return Container(
      margin: const EdgeInsets.only(top: 10),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
        child: Column(
          children: [
            // Introduction section
            Container(
              margin: const EdgeInsets.all(20),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.blue.withValues(alpha: 0.1),
                    Colors.purple.withValues(alpha: 0.1),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.school,
                          color: Colors.blue[700],
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'Your HIV Learning Journey',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Start with "What is HIV?" and follow the numbered steps for the best learning experience. Each topic builds on the previous one to give you a complete understanding.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: Colors.orange[600],
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      const Expanded(
                        child: Text(
                          'Tip: Each topic includes detailed information, practical advice, and helpful resources.',
                          style: TextStyle(
                            fontSize: 12,
                            fontStyle: FontStyle.italic,
                            color: Colors.black54,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Topics list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                itemCount: topics.length,
                itemBuilder: (context, index) {
                  final topic = topics[index];
                  return _buildTopicCard(topic, index);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopicCard(Map<String, dynamic> topic, int index) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300 + (index * 100)),
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              colors: topic['colors'] as List<Color>,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Stack(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Icon(
                            topic['icon'] as IconData,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        if (topic['order'] != null)
                          Positioned(
                            top: -2,
                            right: -2,
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: topic['colors'][0],
                                  width: 2,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  '${topic['order']}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: topic['colors'][0],
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              if (topic['order'] != null) ...[
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    'Step ${topic['order']}',
                                    style: const TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                              ],
                              Expanded(
                                child: Text(
                                  topic['title'] as String,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            topic['subtitle'] as String,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white.withValues(alpha: 0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => _openTopicDetail(topic),
                      icon: const Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  topic['description'] as String,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _openTopicDetail(topic),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: topic['colors'][0],
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        child: const Text('Learn More'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    if (topic['hasVideo'] == true)
                      ElevatedButton.icon(
                        onPressed: () => _playVideo(topic['videoUrl']),
                        icon: const Icon(Icons.play_circle_outline),
                        label: const Text('Video'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white.withValues(alpha: 0.2),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredTopics() {
    final allTopics = [
      {
        'title': 'What is HIV?',
        'subtitle': 'Understanding the basics',
        'description':
            'Learn what HIV means, how it affects your body, and why understanding it is important for everyone.',
        'category': 'basics',
        'icon': Icons.info_outline,
        'colors': [const Color(0xFF667eea), const Color(0xFF764ba2)],
        'hasVideo': true,
        'videoUrl': 'https://www.youtube.com/watch?v=6dVGBBOjGWE',
        'order': 1,
      },
      {
        'title': 'How HIV Spreads',
        'subtitle': 'Transmission explained simply',
        'description':
            'Clear, easy-to-understand information about how HIV is transmitted and how it\'s NOT transmitted.',
        'category': 'transmission',
        'icon': Icons.share,
        'colors': [const Color(0xFFf093fb), const Color(0xFFf5576c)],
        'hasVideo': true,
        'videoUrl': 'https://www.youtube.com/watch?v=Od11QCJPZQ4',
        'order': 2,
      },
      {
        'title': 'HIV Prevention',
        'subtitle': 'Stay safe and protected',
        'description':
            'Simple, effective ways to protect yourself and others from HIV. Learn about safe practices that work.',
        'category': 'prevention',
        'icon': Icons.shield,
        'colors': [const Color(0xFF4facfe), const Color(0xFF00f2fe)],
        'hasVideo': true,
        'videoUrl': 'https://www.youtube.com/watch?v=UtxjXlieaLs',
        'order': 3,
      },
      {
        'title': 'HIV Symptoms',
        'subtitle': 'What to watch for',
        'description':
            'Recognize early signs and symptoms of HIV. Know when to get tested and seek medical care.',
        'category': 'symptoms',
        'icon': Icons.health_and_safety,
        'colors': [const Color(0xFF43e97b), const Color(0xFF38f9d7)],
        'hasVideo': false,
        'order': 4,
      },
      {
        'title': 'HIV Testing',
        'subtitle': 'Know your status',
        'description':
            'Everything about HIV testing: types of tests, where to get tested, and understanding results.',
        'category': 'testing',
        'icon': Icons.medical_services,
        'colors': [const Color(0xFFfa709a), const Color(0xFFfee140)],
        'hasVideo': true,
        'videoUrl': 'https://www.youtube.com/watch?v=yufJNUCRwRs',
        'order': 5,
      },
      {
        'title': 'HIV Treatment',
        'subtitle': 'Living well with HIV',
        'description':
            'Modern HIV treatment is highly effective. Learn how people with HIV can live long, healthy lives.',
        'category': 'treatment',
        'icon': Icons.medication,
        'colors': [const Color(0xFFa8edea), const Color(0xFFfed6e3)],
        'hasVideo': true,
        'videoUrl': 'https://www.youtube.com/watch?v=f2Q4W9gEkZc',
        'order': 6,
      },
      {
        'title': 'Living with HIV',
        'subtitle': 'Daily life and wellness',
        'description':
            'Practical advice for maintaining health, relationships, and quality of life with HIV.',
        'category': 'living',
        'icon': Icons.favorite,
        'colors': [const Color(0xFF667eea), const Color(0xFF764ba2)],
        'hasVideo': false,
        'order': 7,
      },
      {
        'title': 'Support & Resources',
        'subtitle': 'You are not alone',
        'description':
            'Find support groups, counseling services, and resources for people affected by HIV.',
        'category': 'support',
        'icon': Icons.people,
        'colors': [const Color(0xFFf093fb), const Color(0xFFf5576c)],
        'hasVideo': false,
        'order': 8,
      },
      {
        'title': 'Common Myths',
        'subtitle': 'Facts vs Fiction',
        'description':
            'Debunk common myths and misconceptions about HIV with clear, factual information.',
        'category': 'myths_facts',
        'icon': Icons.fact_check,
        'colors': [const Color(0xFF4facfe), const Color(0xFF00f2fe)],
        'hasVideo': true,
        'videoUrl': 'https://www.youtube.com/watch?v=GBB8NSBhTgY',
        'order': 9,
      },
    ];

    var filteredTopics = allTopics;

    // Filter by category
    if (_selectedCategoryIndex > 0) {
      final selectedCategory = _categories[_selectedCategoryIndex];
      filteredTopics = allTopics
          .where((topic) => topic['category'] == selectedCategory)
          .toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filteredTopics = filteredTopics.where((topic) {
        return (topic['title'] as String).toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            (topic['description'] as String).toLowerCase().contains(
              _searchQuery.toLowerCase(),
            );
      }).toList();
    }

    return filteredTopics;
  }

  void _openTopicDetail(Map<String, dynamic> topic) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => TopicDetailScreen(topic: topic)),
    );
  }

  void _playVideo(String? videoUrl) {
    if (videoUrl != null) {
      launchUrl(Uri.parse(videoUrl));
    }
  }
}

class TopicDetailScreen extends StatelessWidget {
  final Map<String, dynamic> topic;

  const TopicDetailScreen({super.key, required this.topic});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: topic['colors'] as List<Color>,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    const SizedBox(width: 12),
                    Icon(
                      topic['icon'] as IconData,
                      color: Colors.white,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        topic['title'] as String,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 20),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                  ),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          topic['subtitle'] as String,
                          style: TextStyle(
                            fontSize: 18,
                            color: topic['colors'][0],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          topic['description'] as String,
                          style: const TextStyle(
                            fontSize: 16,
                            height: 1.6,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Comprehensive content based on topic
                        _buildTopicContent(topic),

                        if (topic['hasVideo'] == true) ...[
                          const SizedBox(height: 24),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: () =>
                                  launchUrl(Uri.parse(topic['videoUrl'])),
                              icon: const Icon(Icons.play_circle_outline),
                              label: const Text('Watch Educational Video'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: topic['colors'][0],
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopicContent(Map<String, dynamic> topic) {
    final category = topic['category'] as String;

    switch (category) {
      case 'basics':
        return _buildBasicsContent(topic);
      case 'transmission':
        return _buildTransmissionContent(topic);
      case 'prevention':
        return _buildPreventionContent(topic);
      case 'symptoms':
        return _buildSymptomsContent(topic);
      case 'testing':
        return _buildTestingContent(topic);
      case 'treatment':
        return _buildTreatmentContent(topic);
      case 'living':
        return _buildLivingContent(topic);
      case 'support':
        return _buildSupportContent(topic);
      case 'myths_facts':
        return _buildMythsFactsContent(topic);
      default:
        return _buildDefaultContent(topic);
    }
  }

  Widget _buildBasicsContent(Map<String, dynamic> topic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: (topic['colors'][0] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'What does HIV stand for?',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text(
                'HIV stands for Human Immunodeficiency Virus.\n\n'
                '• Human: It only affects humans\n'
                '• Immunodeficiency: It weakens your immune system\n'
                '• Virus: It\'s a type of germ that can make you sick',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.psychology, color: Colors.blue),
                  SizedBox(width: 8),
                  Text(
                    'How HIV affects your body:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                '• HIV attacks your immune system (your body\'s defense)\n'
                '• It specifically targets CD4 cells (helper T-cells)\n'
                '• Without treatment, it makes it hard to fight infections\n'
                '• With proper treatment, people live normal, healthy lives\n'
                '• HIV is NOT the same as AIDS',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.lightbulb, color: Colors.green),
                  SizedBox(width: 8),
                  Text(
                    'Important to know:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                '• HIV is a manageable condition with treatment\n'
                '• People with HIV can live long, healthy lives\n'
                '• HIV cannot be cured, but it can be controlled\n'
                '• Early detection and treatment are very important\n'
                '• HIV affects people of all ages, races, and backgrounds',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        _buildResourceLinks([
          {
            'title': 'CDC - About HIV',
            'url': 'https://www.cdc.gov/hiv/basics/whatishiv.html',
            'description': 'Comprehensive HIV information from CDC',
          },
          {
            'title': 'WHO - HIV/AIDS Fact Sheet',
            'url': 'https://www.who.int/news-room/fact-sheets/detail/hiv-aids',
            'description': 'Global HIV facts and statistics',
          },
          {
            'title': 'UNAIDS - HIV Basics',
            'url':
                'https://www.unaids.org/en/frequently-asked-questions-about-hiv-and-aids',
            'description': 'Frequently asked questions about HIV',
          },
        ]),
      ],
    );
  }

  Widget _buildTransmissionContent(Map<String, dynamic> topic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: (topic['colors'][0] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'How HIV spreads (the main ways):',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text(
                '• Unprotected sexual contact (vaginal, anal, or oral)\n'
                '• Sharing needles or syringes\n'
                '• From mother to baby during pregnancy, birth, or breastfeeding\n'
                '• Blood transfusions (very rare in developed countries)\n'
                '• Sharing sharp objects with infected blood',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 8),
                  Text(
                    'HIV does NOT spread through:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                '• Hugging, kissing, or touching\n'
                '• Sharing food, drinks, or utensils\n'
                '• Mosquito or other insect bites\n'
                '• Swimming pools or toilet seats\n'
                '• Coughing, sneezing, or breathing the same air\n'
                '• Casual contact at work, school, or home',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.info, color: Colors.orange),
                  SizedBox(width: 8),
                  Text(
                    'Risk factors that increase transmission:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                '• Having other sexually transmitted infections (STIs)\n'
                '• Having multiple sexual partners\n'
                '• Not using condoms consistently\n'
                '• Sharing drug injection equipment\n'
                '• Having a high viral load (if HIV positive)',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        _buildResourceLinks([
          {
            'title': 'CDC - How HIV Spreads',
            'url': 'https://www.cdc.gov/hiv/basics/transmission.html',
            'description': 'Official CDC information on HIV transmission',
          },
          {
            'title': 'AIDS.gov - HIV Transmission',
            'url':
                'https://www.hiv.gov/hiv-basics/overview/about-hiv-and-aids/how-is-hiv-transmitted',
            'description': 'Government resource on HIV transmission facts',
          },
          {
            'title': 'Avert - HIV Transmission',
            'url':
                'https://www.avert.org/hiv-transmission-prevention/how-you-get-hiv',
            'description': 'Detailed guide on how HIV is transmitted',
          },
        ]),
      ],
    );
  }

  Widget _buildPreventionContent(Map<String, dynamic> topic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: (topic['colors'][0] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Effective Prevention Methods:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text(
                '• Use condoms consistently and correctly\n'
                '• Get tested regularly and know your status\n'
                '• Consider PrEP (Pre-Exposure Prophylaxis) if at high risk\n'
                '• Limit number of sexual partners\n'
                '• Never share needles or syringes\n'
                '• Get vaccinated against other STIs',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.info, color: Colors.orange),
                  SizedBox(width: 8),
                  Text(
                    'Important Facts:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                '• HIV cannot be transmitted through casual contact\n'
                '• Undetectable = Untransmittable (U=U)\n'
                '• PrEP is 99% effective when taken daily\n'
                '• Post-exposure prophylaxis (PEP) available within 72 hours',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        _buildResourceLinks([
          {
            'title': 'CDC - HIV Prevention',
            'url': 'https://www.cdc.gov/hiv/basics/prevention.html',
            'description': 'Comprehensive HIV prevention strategies',
          },
          {
            'title': 'PrEP Information',
            'url': 'https://www.cdc.gov/hiv/risk/prep/index.html',
            'description': 'Pre-exposure prophylaxis (PrEP) information',
          },
          {
            'title': 'Planned Parenthood - HIV Prevention',
            'url':
                'https://www.plannedparenthood.org/learn/stds-hiv-safer-sex/hiv-aids/how-do-i-prevent-hiv',
            'description': 'Practical HIV prevention guide',
          },
        ]),
      ],
    );
  }

  Widget _buildSymptomsContent(Map<String, dynamic> topic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: (topic['colors'][0] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Early HIV Symptoms (2-4 weeks after infection):',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text(
                '• Fever and chills\n'
                '• Rash\n'
                '• Night sweats\n'
                '• Muscle aches\n'
                '• Sore throat\n'
                '• Fatigue\n'
                '• Swollen lymph nodes\n'
                '• Mouth ulcers',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.warning, color: Colors.red),
                  SizedBox(width: 8),
                  Text(
                    'Important Warning:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                'Many people with HIV have no symptoms for years. The only way to know for sure is to get tested. Early symptoms are similar to flu and may be mild.',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        _buildResourceLinks([
          {
            'title': 'CDC - HIV Symptoms',
            'url': 'https://www.cdc.gov/hiv/basics/whatishiv.html#symptoms',
            'description': 'Official information on HIV symptoms',
          },
          {
            'title': 'WebMD - HIV Symptoms',
            'url': 'https://www.webmd.com/hiv-aids/hiv-symptoms',
            'description': 'Detailed guide to HIV symptoms and stages',
          },
          {
            'title': 'Healthline - Early HIV Symptoms',
            'url':
                'https://www.healthline.com/health/hiv-aids/early-signs-hiv-infection',
            'description': 'Early signs and symptoms of HIV infection',
          },
        ]),
      ],
    );
  }

  Widget _buildTestingContent(Map<String, dynamic> topic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: (topic['colors'][0] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Types of HIV Tests:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text(
                '• Antibody tests (most common)\n'
                '• Antigen/antibody tests (faster detection)\n'
                '• Nucleic acid tests (earliest detection)\n'
                '• Rapid tests (results in 20 minutes)\n'
                '• Home testing kits (private and convenient)',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.schedule, color: Colors.green),
                  SizedBox(width: 8),
                  Text(
                    'Testing Timeline:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                '• Window period: 10 days to 3 months\n'
                '• Get tested annually if sexually active\n'
                '• Test more frequently if high risk\n'
                '• Test after potential exposure\n'
                '• Free and confidential testing available',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        _buildResourceLinks([
          {
            'title': 'CDC - HIV Testing',
            'url': 'https://www.cdc.gov/hiv/testing/index.html',
            'description': 'Complete guide to HIV testing',
          },
          {
            'title': 'HIV Test Locator',
            'url': 'https://gettested.cdc.gov/',
            'description': 'Find HIV testing locations near you',
          },
          {
            'title': 'Home HIV Test Information',
            'url':
                'https://www.fda.gov/consumers/consumer-updates/home-hiv-test-frequently-asked-questions',
            'description': 'FDA information on home HIV testing',
          },
        ]),
      ],
    );
  }

  Widget _buildTreatmentContent(Map<String, dynamic> topic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: (topic['colors'][0] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Antiretroviral Therapy (ART):',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text(
                '• Combination of HIV medicines taken daily\n'
                '• Reduces viral load to undetectable levels\n'
                '• Prevents progression to AIDS\n'
                '• Allows normal life expectancy\n'
                '• Prevents transmission to others (U=U)\n'
                '• Side effects are generally manageable',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.medical_services, color: Colors.blue),
                  SizedBox(width: 8),
                  Text(
                    'Treatment Success:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                '• Start treatment as soon as possible after diagnosis\n'
                '• Take medications exactly as prescribed\n'
                '• Regular monitoring and lab tests\n'
                '• Adherence is key to treatment success\n'
                '• Modern treatments are highly effective',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        _buildResourceLinks([
          {
            'title': 'CDC - HIV Treatment',
            'url':
                'https://www.cdc.gov/hiv/basics/livingwithhiv/treatment.html',
            'description': 'Official HIV treatment information',
          },
          {
            'title': 'HIV.gov - Treatment',
            'url':
                'https://www.hiv.gov/hiv-basics/staying-in-hiv-care/hiv-treatment/hiv-treatment-overview',
            'description': 'Comprehensive HIV treatment overview',
          },
          {
            'title': 'UNAIDS - Treatment',
            'url': 'https://www.unaids.org/en/topic/treatment',
            'description': 'Global perspective on HIV treatment',
          },
        ]),
      ],
    );
  }

  Widget _buildLivingContent(Map<String, dynamic> topic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: (topic['colors'][0] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Daily Life with HIV:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text(
                '• Take your medications exactly as prescribed\n'
                '• Keep regular medical appointments\n'
                '• Maintain a healthy diet and exercise regularly\n'
                '• Get enough sleep and manage stress\n'
                '• Stay up to date with vaccinations\n'
                '• Practice safe sex to protect others',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.purple.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.favorite, color: Colors.purple),
                  SizedBox(width: 8),
                  Text(
                    'Relationships and Family:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                '• You can have healthy relationships and family life\n'
                '• Discuss your status with trusted partners\n'
                '• With treatment, you can have children safely\n'
                '• Undetectable = Untransmittable (U=U)\n'
                '• Open communication builds stronger relationships',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.teal.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.teal.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.work, color: Colors.teal),
                  SizedBox(width: 8),
                  Text(
                    'Work and Social Life:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                '• You can continue working and pursuing goals\n'
                '• HIV status is private medical information\n'
                '• Discrimination based on HIV status is illegal\n'
                '• Stay connected with friends and community\n'
                '• Consider joining support groups',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        _buildResourceLinks([
          {
            'title': 'CDC - Living with HIV',
            'url': 'https://www.cdc.gov/hiv/basics/livingwithhiv/index.html',
            'description': 'Complete guide to living well with HIV',
          },
          {
            'title': 'The Well Project',
            'url': 'https://www.thewellproject.org/',
            'description': 'HIV information for women and families',
          },
          {
            'title': 'POZ Magazine',
            'url': 'https://www.poz.com/',
            'description': 'HIV-positive community and lifestyle magazine',
          },
        ]),
      ],
    );
  }

  Widget _buildSupportContent(Map<String, dynamic> topic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: (topic['colors'][0] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Support Resources:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text(
                '• HIV support groups and communities\n'
                '• Mental health counseling services\n'
                '• Peer support programs\n'
                '• Online forums and resources\n'
                '• Family and friends support\n'
                '• Healthcare team coordination',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.purple.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.favorite, color: Colors.purple),
                  SizedBox(width: 8),
                  Text(
                    'Living Well with HIV:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                '• Maintain a healthy lifestyle\n'
                '• Stay connected with loved ones\n'
                '• Continue pursuing goals and dreams\n'
                '• Practice stress management\n'
                '• Stay informed about HIV advances',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        _buildResourceLinks([
          {
            'title': 'AIDS.gov - Support',
            'url':
                'https://www.hiv.gov/hiv-basics/staying-in-hiv-care/other-related-health-issues/mental-health',
            'description': 'Mental health and support resources',
          },
          {
            'title': 'National AIDS Hotline',
            'url':
                'https://www.cdc.gov/hiv/basics/hiv-testing/test-for-hiv.html#hotline',
            'description': 'Free, confidential HIV information hotline',
          },
          {
            'title': 'HIV Support Groups',
            'url':
                'https://www.aids.gov/hiv-aids-basics/just-diagnosed-with-hiv-aids/hiv-aids-support-groups/',
            'description': 'Find local HIV support groups',
          },
        ]),
      ],
    );
  }

  Widget _buildMythsFactsContent(Map<String, dynamic> topic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: (topic['colors'][0] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Common Myths vs Facts:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text(
                'MYTH: HIV can be transmitted through casual contact\n'
                'FACT: HIV cannot be transmitted through hugging, shaking hands, or sharing utensils\n\n'
                'MYTH: HIV is a death sentence\n'
                'FACT: With proper treatment, people with HIV can live normal lifespans\n\n'
                'MYTH: Only certain groups get HIV\n'
                'FACT: Anyone can get HIV regardless of age, race, or sexual orientation',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.teal.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.teal.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.lightbulb, color: Colors.teal),
                  SizedBox(width: 8),
                  Text(
                    'Key Facts:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Text(
                '• HIV is transmitted through specific body fluids\n'
                '• Modern HIV treatment is highly effective\n'
                '• People with undetectable viral loads cannot transmit HIV\n'
                '• HIV testing is confidential and widely available\n'
                '• Stigma and discrimination are not acceptable',
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        _buildResourceLinks([
          {
            'title': 'CDC - HIV Myths and Facts',
            'url':
                'https://www.cdc.gov/hiv/basics/hiv-prevention/reduce-risk/myths.html',
            'description': 'Debunking common HIV myths with facts',
          },
          {
            'title': 'UNAIDS - Myths and Misconceptions',
            'url':
                'https://www.unaids.org/en/frequently-asked-questions-about-hiv-and-aids',
            'description': 'Global perspective on HIV myths and facts',
          },
          {
            'title': 'Avert - HIV Myths',
            'url': 'https://www.avert.org/hiv-aids-myths-stigma',
            'description': 'Comprehensive guide to HIV myths and stigma',
          },
        ]),
      ],
    );
  }

  Widget _buildDefaultContent(Map<String, dynamic> topic) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: (topic['colors'][0] as Color).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Key Information:',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Text(
            topic['description'] as String,
            style: const TextStyle(fontSize: 16, height: 1.5),
          ),
        ],
      ),
    );
  }

  Widget _buildResourceLinks(List<Map<String, String>> resources) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.indigo.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.indigo.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.link, color: Colors.indigo),
              SizedBox(width: 8),
              Text(
                'Additional Resources & Links',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...resources.map(
            (resource) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: InkWell(
                onTap: () => launchUrl(Uri.parse(resource['url']!)),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.indigo.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.open_in_new,
                        color: Colors.indigo,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              resource['title']!,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.indigo,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              resource['description']!,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
