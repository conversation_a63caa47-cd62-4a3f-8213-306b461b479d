@echo off
title HIV Predictor - QR Installation Server
color 0A

echo.
echo ========================================
echo   🏥 HIV Predictor - QR Installation
echo ========================================
echo.

REM Check if APK exists
echo 🔍 Checking for APK file...
if exist "build\app\outputs\flutter-apk\app-debug.apk" (
    echo ✅ APK found: app-debug.apk
    set APK_SIZE=
    for %%A in ("build\app\outputs\flutter-apk\app-debug.apk") do set APK_SIZE=%%~zA
    echo 📦 APK Size: %APK_SIZE% bytes
) else (
    echo ❌ APK not found!
    echo 🔨 Please build the APK first with: flutter build apk --debug
    echo.
    pause
    exit /b 1
)

echo.
echo 🌐 Starting QR Installation Server...
echo.

REM Get local IP address
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set LOCAL_IP=%%a
    goto :found_ip
)
:found_ip
set LOCAL_IP=%LOCAL_IP: =%

echo 📡 Server Details:
echo    • Local IP: %LOCAL_IP%
echo    • Port: 8080
echo    • APK URL: http://%LOCAL_IP%:8080/app-debug.apk
echo    • QR Page: http://%LOCAL_IP%:8080/qr_download_page.html
echo.

echo 📱 Instructions:
echo    1. Make sure your phone and PC are on the SAME WiFi network
echo    2. Open the QR page in your browser (will open automatically)
echo    3. Scan the QR code with your phone's camera
echo    4. Follow the installation steps on your phone
echo.

echo 🚀 Starting server...
echo    Press Ctrl+C to stop the server
echo.

REM Copy APK to root directory for easier access
copy "build\app\outputs\flutter-apk\app-debug.apk" "app-debug.apk" >nul 2>&1

REM Open QR page in browser
start http://localhost:8080/qr_download_page.html

REM Start Python HTTP server
python -m http.server 8080

echo.
echo 🛑 Server stopped.
pause
