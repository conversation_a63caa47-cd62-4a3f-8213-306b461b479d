@echo off
echo ========================================
echo   HIV Predictor - APK Builder
echo ========================================
echo.

echo 🔧 Building APK for Android...
echo 📱 Target: Infinix X6517 (Android 12)
echo.

REM Clean previous builds
echo 🧹 Cleaning previous builds...
flutter clean

echo 📦 Getting dependencies...
flutter pub get

echo 🔨 Building debug APK...
flutter build apk --debug

echo.
echo ✅ APK Build Complete!
echo 📁 APK Location: build\app\outputs\flutter-apk\app-debug.apk
echo.
echo 📱 To install on your device:
echo    1. Copy the APK to your phone
echo    2. Enable "Install from unknown sources" in Settings
echo    3. Tap the APK file to install
echo.
pause
