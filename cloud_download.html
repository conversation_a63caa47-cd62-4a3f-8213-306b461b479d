
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HIV Predictor - Cloud Download</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .app-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            margin: 20px 0;
        }
        
        .download-option {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
            border-left: 5px solid #4CAF50;
        }
        
        .download-btn {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .cloud-btn {
            background: #2196F3;
        }
        
        .cloud-btn:hover {
            background: #1976D2;
        }
        
        .usb-btn {
            background: #FF9800;
        }
        
        .usb-btn:hover {
            background: #F57C00;
        }
        
        .steps {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
        }
        
        .steps ol {
            padding-left: 20px;
        }
        
        .steps li {
            margin: 8px 0;
            line-height: 1.4;
        }
        
        .file-info {
            background: rgba(76, 175, 80, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border: 1px solid #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="app-icon">🏥</div>
            <h1>HIV Predictor v1.1.0</h1>
            <p>Professional Medical App with AI Risk Assessment</p>
        </div>
        
        <div class="card">
            <h2>📱 Download Options</h2>
            <p>Choose the method that works best for you:</p>
            
            <div class="download-option">
                <h3>🌐 Option 1: Same WiFi Network (Fastest)</h3>
                <p>If your phone and computer are on the same WiFi:</p>
                <a href="http://172.30.18.51:8080/app-debug.apk" class="download-btn">
                    📱 Download APK (Same WiFi)
                </a>
                <div class="steps">
                    <ol>
                        <li>Make sure phone and computer are on same WiFi</li>
                        <li>Click the download button above</li>
                        <li>Install the APK on your phone</li>
                    </ol>
                </div>
            </div>
            
            <div class="download-option">
                <h3>☁️ Option 2: Cloud Storage (Universal)</h3>
                <p>Works from anywhere with internet:</p>
                <a href="#" class="download-btn cloud-btn" onclick="showCloudInstructions()">
                    ☁️ Upload to Cloud Storage
                </a>
                <div class="steps" id="cloud-steps" style="display: none;">
                    <p><strong>Manual Upload Instructions:</strong></p>
                    <ol>
                        <li>Go to <a href="https://drive.google.com" target="_blank">Google Drive</a> or <a href="https://dropbox.com" target="_blank">Dropbox</a></li>
                        <li>Upload the APK file: <code>app-debug.apk</code></li>
                        <li>Share the link publicly</li>
                        <li>Access from your phone anywhere</li>
                    </ol>
                </div>
            </div>
            
            <div class="download-option">
                <h3>🔌 Option 3: USB Transfer (No WiFi Needed)</h3>
                <p>Direct transfer via USB cable:</p>
                <a href="#" class="download-btn usb-btn" onclick="showUSBInstructions()">
                    🔌 USB Transfer Guide
                </a>
                <div class="steps" id="usb-steps" style="display: none;">
                    <ol>
                        <li>Connect your phone to computer with USB cable</li>
                        <li>Enable "File Transfer" mode on phone</li>
                        <li>Copy <code>app-debug.apk</code> to phone storage</li>
                        <li>Open file manager on phone</li>
                        <li>Find and tap the APK file to install</li>
                    </ol>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>📋 File Information</h2>
            <div class="file-info">
                <p><strong>📱 File:</strong> app-debug.apk</p>
                <p><strong>📏 Size:</strong> ~15-20 MB</p>
                <p><strong>🎯 Version:</strong> HIV Predictor v1.1.0</p>
                <p><strong>🎨 Features:</strong> Professional logo, AI assessment, Hospital finder</p>
                <p><strong>📍 Location:</strong> C:\Users\<USER>\Desktop\hiv_predictor_app\app-debug.apk</p>
            </div>
        </div>
        
        <div class="card">
            <h2>🎨 What's New in v1.1.0</h2>
            <ul>
                <li>✨ Professional medical logo with blue gradient</li>
                <li>🏥 HIV awareness ribbon and AI indicator</li>
                <li>🚀 Updated to Flutter 3.32.7 for better performance</li>
                <li>⚠️ Major build warnings fixed</li>
                <li>🔧 Enhanced stability and faster loading</li>
            </ul>
        </div>
    </div>

    <script>
        function showCloudInstructions() {
            const steps = document.getElementById('cloud-steps');
            steps.style.display = steps.style.display === 'none' ? 'block' : 'none';
        }
        
        function showUSBInstructions() {
            const steps = document.getElementById('usb-steps');
            steps.style.display = steps.style.display === 'none' ? 'block' : 'none';
        }
    </script>
</body>
</html>
