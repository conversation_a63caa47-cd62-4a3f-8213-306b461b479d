# 🔥 Firebase Security Rules Setup - URGENT

## ⚠️ CRITICAL: Your Firestore database expires in 2 days!

### Step 1: Access Firebase Console
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: **hiv-predictor-app**
3. Navigate to **Firestore Database**

### Step 2: Update Security Rules
1. Click on **"Rules"** tab in Firestore
2. **Replace the current rules** with the content from `firestore_security_rules.rules`
3. Click **"Publish"** to apply the new rules

### Step 3: Set Up Admin Users (Important!)
Before applying the rules, you need to create admin users:

```javascript
// Run this in Firebase Console > Firestore > Data
// Create a new collection called "admins"
// Add documents with admin user IDs

Collection: admins
Document ID: YOUR_ADMIN_USER_ID (from Firebase Auth)
Fields: {
  email: "<EMAIL>",
  role: "admin",
  createdAt: [current timestamp],
  permissions: ["read_all", "write_all", "manage_users"]
}
```

### Step 4: Test the Rules
1. Use the **Rules Playground** in Firebase Console
2. Test different scenarios:
   - Authenticated user accessing their own data ✅
   - User trying to access another user's data ❌
   - Admin accessing all data ✅
   - Unauthenticated access to education content ✅

### Step 5: Verify App Functionality
After applying rules, test your app:
- ✅ User registration/login
- ✅ Prediction creation and retrieval
- ✅ Dashboard data loading
- ✅ Education content access
- ✅ Admin dashboard (if applicable)

## 🛡️ Security Features Implemented

### ✅ User Data Protection
- Users can only access their own data
- Predictions are user-specific
- Personal information is protected

### ✅ Admin Access Control
- Admins can read all data for monitoring
- Admin-only collections for management
- Secure admin verification

### ✅ Public Education Access
- Education content remains publicly accessible
- No authentication required for learning materials

### ✅ Audit Trail
- All operations are logged
- Error tracking for debugging
- Analytics for app improvement

## 🚨 IMMEDIATE ACTION REQUIRED

### Option 1: Quick Fix (Recommended)
1. Copy the rules from `firestore_security_rules.rules`
2. Paste into Firebase Console > Firestore > Rules
3. Click "Publish"
4. Create admin user documents

### Option 2: Extend Test Mode (Temporary)
If you need more time to test:
1. Go to Firestore Rules
2. Modify the date in the current rule to extend by 30 days
3. **This is NOT recommended for production!**

## 📋 Post-Implementation Checklist

- [ ] Rules published successfully
- [ ] Admin users created in Firestore
- [ ] App authentication working
- [ ] User data access working
- [ ] Admin dashboard functional
- [ ] Education content accessible
- [ ] No unauthorized access possible

## 🔧 Troubleshooting

### If app stops working after rules update:
1. Check Firebase Console > Firestore > Usage tab for denied requests
2. Verify user authentication is working
3. Ensure admin documents are created correctly
4. Test rules in Rules Playground

### Common Issues:
- **Permission denied**: User not authenticated or accessing wrong data
- **Admin access failed**: Admin document not created in Firestore
- **Education not loading**: Check if rules allow public read access

## 📞 Support
If you encounter issues:
1. Check Firebase Console logs
2. Verify authentication status in app
3. Test rules in Firebase Console playground
4. Ensure all required collections exist

**⏰ DEADLINE: 2 DAYS - Apply these rules immediately to prevent data access loss!**
