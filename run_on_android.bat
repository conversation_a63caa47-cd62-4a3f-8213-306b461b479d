@echo off
echo ========================================
echo   HIV Predictor - Running on Android
echo ========================================
echo.

REM Check if device is connected
echo 🔍 Checking connected devices...
C:\flutter\bin\flutter.bat devices

echo.
echo 🚀 Starting HIV Predictor app on your Android device...
echo.
echo 📱 Your Infinix X6517 device should show the app shortly
echo 💡 Make sure USB debugging is enabled on your device
echo.

REM Run the app on Android device
C:\flutter\bin\flutter.bat run -d 0966054336007596

echo.
echo 👋 App deployment completed
pause
