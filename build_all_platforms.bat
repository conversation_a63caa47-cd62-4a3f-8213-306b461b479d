@echo off
title HIV Predictor - Multi-Platform Builder
color 0A

echo.
echo ========================================
echo   🏥 HIV Predictor - Multi-Platform Build
echo ========================================
echo.

echo 🔧 Building for all supported platforms...
echo.

REM Clean previous builds
echo 🧹 Cleaning previous builds...
flutter clean
flutter pub get

echo.
echo 📱 Building Android APK...
flutter build apk --debug --target-platform android-arm64
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Android build failed
    pause
    exit /b 1
)

echo.
echo 🍎 Building iOS (requires macOS with Xcode)...
flutter build ios --debug --no-codesign
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ iOS build failed (requires macOS with Xcode)
) else (
    echo ✅ iOS build completed
)

echo.
echo 🖥️ Building macOS (requires macOS)...
flutter build macos --debug
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ macOS build failed (requires macOS)
) else (
    echo ✅ macOS build completed
)

echo.
echo 🌐 Building Web version...
flutter build web --debug
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Web build failed
) else (
    echo ✅ Web build completed
)

echo.
echo 🪟 Building Windows (if supported)...
flutter build windows --debug
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ Windows build failed or not supported
) else (
    echo ✅ Windows build completed
)

echo.
echo ========================================
echo   📦 Build Summary
echo ========================================
echo.

if exist "build\app\outputs\flutter-apk\app-debug.apk" (
    echo ✅ Android APK: build\app\outputs\flutter-apk\app-debug.apk
) else (
    echo ❌ Android APK: Failed
)

if exist "build\ios\iphoneos\Runner.app" (
    echo ✅ iOS App: build\ios\iphoneos\Runner.app
) else (
    echo ⚠️ iOS App: Not built (requires macOS)
)

if exist "build\macos\Build\Products\Debug\hiv_predictor_app.app" (
    echo ✅ macOS App: build\macos\Build\Products\Debug\hiv_predictor_app.app
) else (
    echo ⚠️ macOS App: Not built (requires macOS)
)

if exist "build\web\index.html" (
    echo ✅ Web App: build\web\index.html
) else (
    echo ❌ Web App: Failed
)

if exist "build\windows\x64\runner\Debug\hiv_predictor_app.exe" (
    echo ✅ Windows App: build\windows\x64\runner\Debug\hiv_predictor_app.exe
) else (
    echo ⚠️ Windows App: Not built
)

echo.
echo 🚀 Multi-platform build process completed!
echo.
pause
