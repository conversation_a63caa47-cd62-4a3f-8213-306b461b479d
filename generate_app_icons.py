#!/usr/bin/env python3
"""
HIV Predictor App - Icon Generator
Creates app icons in all required sizes for Android and iOS
"""

from PIL import Image, ImageDraw
import os

def create_medical_logo(size):
    """Create the HIV Predictor medical logo"""
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Calculate proportional sizes
    margin = size // 10
    logo_size = size - (margin * 2)
    center = size // 2
    
    # Background rounded rectangle (blue)
    bg_color = (25, 118, 210, 255)  # #1976D2
    corner_radius = size // 8
    
    # Draw rounded rectangle background
    draw.rounded_rectangle(
        [margin, margin, size - margin, size - margin],
        radius=corner_radius,
        fill=bg_color
    )
    
    # Inner gradient effect (lighter blue)
    inner_margin = margin + size // 20
    inner_color = (33, 150, 243, 255)  # #2196F3
    inner_radius = corner_radius - size // 40
    
    draw.rounded_rectangle(
        [inner_margin, inner_margin, size - inner_margin, size - inner_margin],
        radius=inner_radius,
        fill=inner_color
    )
    
    # Medical cross (white)
    cross_color = (255, 255, 255, 255)
    cross_width = size // 8
    cross_length = size // 2
    cross_radius = size // 32
    
    # Vertical part of cross
    v_left = center - cross_width // 2
    v_right = center + cross_width // 2
    v_top = center - cross_length // 2
    v_bottom = center + cross_length // 2
    
    draw.rounded_rectangle(
        [v_left, v_top, v_right, v_bottom],
        radius=cross_radius,
        fill=cross_color
    )
    
    # Horizontal part of cross
    h_left = center - cross_length // 2
    h_right = center + cross_length // 2
    h_top = center - cross_width // 2
    h_bottom = center + cross_width // 2
    
    draw.rounded_rectangle(
        [h_left, h_top, h_right, h_bottom],
        radius=cross_radius,
        fill=cross_color
    )
    
    # HIV awareness ribbon (red)
    ribbon_color = (220, 20, 60, 255)  # #DC143C
    ribbon_y = margin + size // 6
    ribbon_height = size // 12
    
    # Simple ribbon shape
    ribbon_points = [
        (center - size // 4, ribbon_y),
        (center + size // 4, ribbon_y),
        (center + size // 5, ribbon_y + ribbon_height),
        (center - size // 5, ribbon_y + ribbon_height)
    ]
    draw.polygon(ribbon_points, fill=ribbon_color)
    
    # AI indicator (green circle)
    ai_color = (76, 175, 80, 255)  # #4CAF50
    ai_radius = size // 16
    ai_x = size - margin - ai_radius * 2
    ai_y = margin + ai_radius
    
    draw.ellipse(
        [ai_x - ai_radius, ai_y - ai_radius, ai_x + ai_radius, ai_y + ai_radius],
        fill=ai_color
    )
    
    # AI text (white) - simplified as small rectangle
    ai_text_size = ai_radius // 2
    draw.rectangle(
        [ai_x - ai_text_size, ai_y - ai_text_size//2, ai_x + ai_text_size, ai_y + ai_text_size//2],
        fill=(255, 255, 255, 255)
    )
    
    return img

def create_android_icons():
    """Create Android app icons in all required sizes"""
    android_sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192
    }
    
    print("🤖 Creating Android app icons...")
    
    for folder, size in android_sizes.items():
        # Create directory if it doesn't exist
        icon_dir = f"android/app/src/main/res/{folder}"
        os.makedirs(icon_dir, exist_ok=True)
        
        # Generate icon
        icon = create_medical_logo(size)
        icon_path = f"{icon_dir}/ic_launcher.png"
        icon.save(icon_path, "PNG")
        print(f"✅ Created {icon_path} ({size}x{size})")
        
        # Also create adaptive icon background and foreground
        # Background (solid color)
        bg = Image.new('RGBA', (size, size), (25, 118, 210, 255))
        bg_path = f"{icon_dir}/ic_launcher_background.png"
        bg.save(bg_path, "PNG")
        
        # Foreground (logo without background)
        fg = create_medical_logo_foreground(size)
        fg_path = f"{icon_dir}/ic_launcher_foreground.png"
        fg.save(fg_path, "PNG")

def create_medical_logo_foreground(size):
    """Create foreground part of the logo for adaptive icons"""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center = size // 2
    
    # Medical cross (white)
    cross_color = (255, 255, 255, 255)
    cross_width = size // 6
    cross_length = size // 2.5
    cross_radius = size // 32
    
    # Vertical part of cross
    v_left = center - cross_width // 2
    v_right = center + cross_width // 2
    v_top = center - cross_length // 2
    v_bottom = center + cross_length // 2
    
    draw.rounded_rectangle(
        [v_left, v_top, v_right, v_bottom],
        radius=cross_radius,
        fill=cross_color
    )
    
    # Horizontal part of cross
    h_left = center - cross_length // 2
    h_right = center + cross_length // 2
    h_top = center - cross_width // 2
    h_bottom = center + cross_width // 2
    
    draw.rounded_rectangle(
        [h_left, h_top, h_right, h_bottom],
        radius=cross_radius,
        fill=cross_color
    )
    
    return img

def create_ios_icons():
    """Create iOS app icons in all required sizes"""
    ios_sizes = {
        'Icon-App-20x20@1x': 20,
        'Icon-App-20x20@2x': 40,
        'Icon-App-20x20@3x': 60,
        'Icon-App-29x29@1x': 29,
        'Icon-App-29x29@2x': 58,
        'Icon-App-29x29@3x': 87,
        'Icon-App-40x40@1x': 40,
        'Icon-App-40x40@2x': 80,
        'Icon-App-40x40@3x': 120,
        'Icon-App-60x60@2x': 120,
        'Icon-App-60x60@3x': 180,
        'Icon-App-76x76@1x': 76,
        'Icon-App-76x76@2x': 152,
        'Icon-App-83.5x83.5@2x': 167,
        'Icon-App-1024x1024@1x': 1024
    }
    
    print("🍎 Creating iOS app icons...")
    
    # Create directory
    icon_dir = "ios/Runner/Assets.xcassets/AppIcon.appiconset"
    os.makedirs(icon_dir, exist_ok=True)
    
    for name, size in ios_sizes.items():
        icon = create_medical_logo(size)
        icon_path = f"{icon_dir}/{name}.png"
        icon.save(icon_path, "PNG")
        print(f"✅ Created {icon_path} ({size}x{size})")

def create_web_icons():
    """Create web app icons"""
    web_sizes = [16, 32, 48, 72, 96, 144, 192, 512]
    
    print("🌐 Creating web app icons...")
    
    # Create directory
    icon_dir = "web/icons"
    os.makedirs(icon_dir, exist_ok=True)
    
    for size in web_sizes:
        icon = create_medical_logo(size)
        icon_path = f"{icon_dir}/Icon-{size}.png"
        icon.save(icon_path, "PNG")
        print(f"✅ Created {icon_path} ({size}x{size})")
    
    # Create favicon
    favicon = create_medical_logo(32)
    favicon.save("web/favicon.png", "PNG")
    print("✅ Created web/favicon.png")

def create_adaptive_icon_xml():
    """Create Android adaptive icon XML files"""
    
    # ic_launcher.xml
    launcher_xml = '''<?xml version="1.0" encoding="utf-8"?>
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
    <background android:drawable="@mipmap/ic_launcher_background"/>
    <foreground android:drawable="@mipmap/ic_launcher_foreground"/>
</adaptive-icon>'''
    
    # Create for all density folders
    densities = ['mipmap-mdpi', 'mipmap-hdpi', 'mipmap-xhdpi', 'mipmap-xxhdpi', 'mipmap-xxxhdpi']
    
    for density in densities:
        xml_dir = f"android/app/src/main/res/{density}"
        os.makedirs(xml_dir, exist_ok=True)
        
        with open(f"{xml_dir}/ic_launcher.xml", 'w') as f:
            f.write(launcher_xml)
        print(f"✅ Created {xml_dir}/ic_launcher.xml")

def main():
    """Generate all app icons"""
    print("🎨 HIV Predictor App - Icon Generator")
    print("=" * 50)
    
    try:
        create_android_icons()
        create_adaptive_icon_xml()
        create_ios_icons()
        create_web_icons()
        
        print("\n" + "=" * 50)
        print("🎉 All app icons created successfully!")
        print("\n📱 Android icons: android/app/src/main/res/mipmap-*/")
        print("🍎 iOS icons: ios/Runner/Assets.xcassets/AppIcon.appiconset/")
        print("🌐 Web icons: web/icons/")
        print("\n🔄 Rebuild your app to see the new icons!")
        
    except Exception as e:
        print(f"❌ Error creating icons: {e}")
        print("💡 Make sure you have Pillow installed: pip install Pillow")

if __name__ == "__main__":
    main()
