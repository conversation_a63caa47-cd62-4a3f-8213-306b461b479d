#!/usr/bin/env python3
"""
Multi-Platform HIV Predictor Distribution Server
Serves Android APK, iOS IPA, Web App, and provides QR codes
"""

import http.server
import socketserver
import qrcode
import os
import webbrowser
import socket
from pathlib import Path
import shutil

def get_local_ip():
    """Get the local IP address"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "*************"

def create_multi_platform_page():
    """Create a comprehensive download page for all platforms"""
    local_ip = get_local_ip()
    port = 8080
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HIV Predictor - Multi-Platform Download</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 40px;
        }}
        
        .app-icon {{
            font-size: 80px;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }}
        
        h1 {{
            font-size: 3em;
            margin-bottom: 10px;
            font-weight: 300;
        }}
        
        .subtitle {{
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }}
        
        .platforms {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }}
        
        .platform-card {{
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            text-align: center;
            transition: transform 0.3s ease;
        }}
        
        .platform-card:hover {{
            transform: translateY(-5px);
        }}
        
        .platform-icon {{
            font-size: 60px;
            margin-bottom: 20px;
        }}
        
        .platform-title {{
            font-size: 1.5em;
            margin-bottom: 10px;
            font-weight: bold;
        }}
        
        .platform-desc {{
            opacity: 0.8;
            margin-bottom: 20px;
            line-height: 1.4;
        }}
        
        .download-btn {{
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }}
        
        .download-btn:hover {{
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }}
        
        .web-btn {{
            background: #2196F3;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }}
        
        .web-btn:hover {{
            background: #1976D2;
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }}
        
        .qr-container {{
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px auto;
            display: inline-block;
        }}
        
        .features {{
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            margin: 40px 0;
        }}
        
        .features h3 {{
            text-align: center;
            margin-bottom: 20px;
            color: #FFD700;
        }}
        
        .feature-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }}
        
        .feature-item {{
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
        }}
        
        .instructions {{
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            margin: 40px 0;
        }}
        
        .instructions h3 {{
            color: #FFD700;
            margin-bottom: 15px;
        }}
        
        .instructions ol {{
            padding-left: 20px;
        }}
        
        .instructions li {{
            margin: 8px 0;
            line-height: 1.4;
        }}
        
        @media (max-width: 768px) {{
            .platforms {{
                grid-template-columns: 1fr;
            }}
            
            h1 {{
                font-size: 2em;
            }}
            
            .platform-card {{
                padding: 20px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="app-icon">🏥</div>
            <h1>HIV Predictor</h1>
            <p class="subtitle">AI-powered HIV risk assessment and healthcare navigation</p>
            <p class="subtitle">Available on all your devices</p>
        </div>
        
        <div class="platforms">
            <!-- Android -->
            <div class="platform-card">
                <div class="platform-icon">📱</div>
                <div class="platform-title">Android</div>
                <div class="platform-desc">
                    Full-featured mobile app with GPS hospital finder, 
                    offline capabilities, and push notifications.
                </div>
                <div class="qr-container">
                    <canvas id="android-qr" width="150" height="150"></canvas>
                </div>
                <a href="http://{local_ip}:{port}/app-debug.apk" class="download-btn">
                    📱 Download APK
                </a>
            </div>
            
            <!-- iOS -->
            <div class="platform-card">
                <div class="platform-icon">🍎</div>
                <div class="platform-title">iOS / iPhone</div>
                <div class="platform-desc">
                    Native iOS app with location services, 
                    Apple Health integration, and iOS notifications.
                </div>
                <div class="qr-container">
                    <canvas id="ios-qr" width="150" height="150"></canvas>
                </div>
                <a href="http://{local_ip}:{port}/ios-install.html" class="download-btn">
                    🍎 Install on iOS
                </a>
            </div>
            
            <!-- Web App -->
            <div class="platform-card">
                <div class="platform-icon">🌐</div>
                <div class="platform-title">Web App</div>
                <div class="platform-desc">
                    Works on any device with a browser. 
                    No installation required. Progressive Web App.
                </div>
                <div class="qr-container">
                    <canvas id="web-qr" width="150" height="150"></canvas>
                </div>
                <a href="http://{local_ip}:{port}/web/" class="download-btn web-btn">
                    🌐 Open Web App
                </a>
            </div>
            
            <!-- macOS -->
            <div class="platform-card">
                <div class="platform-icon">🖥️</div>
                <div class="platform-title">macOS</div>
                <div class="platform-desc">
                    Native macOS application with full desktop features,
                    menu bar integration, and macOS notifications.
                </div>
                <div class="qr-container">
                    <canvas id="macos-qr" width="150" height="150"></canvas>
                </div>
                <a href="http://{local_ip}:{port}/macos-install.html" class="download-btn">
                    🖥️ Download for Mac
                </a>
            </div>
        </div>
        
        <div class="features">
            <h3>🎯 App Features</h3>
            <div class="feature-grid">
                <div class="feature-item">
                    <strong>🔬 AI Risk Assessment</strong><br>
                    22 enhanced symptoms with ML prediction
                </div>
                <div class="feature-item">
                    <strong>🗺️ Hospital Finder</strong><br>
                    Real Rwanda hospitals with GPS navigation
                </div>
                <div class="feature-item">
                    <strong>📚 HIV Education</strong><br>
                    Comprehensive information and resources
                </div>
                <div class="feature-item">
                    <strong>🔐 Secure Data</strong><br>
                    Firebase authentication and encryption
                </div>
                <div class="feature-item">
                    <strong>👨‍⚕️ Admin Dashboard</strong><br>
                    Healthcare provider management tools
                </div>
                <div class="feature-item">
                    <strong>🌍 English Interface</strong><br>
                    Clean, user-friendly design
                </div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>📱 Installation Instructions</h3>
            <ol>
                <li><strong>Choose your platform</strong> from the options above</li>
                <li><strong>Scan the QR code</strong> with your device camera</li>
                <li><strong>Follow platform-specific</strong> installation steps</li>
                <li><strong>Allow location permissions</strong> for hospital finder</li>
                <li><strong>Create account or login</strong> to access all features</li>
            </ol>
        </div>
    </div>

    <script>
        // Generate QR codes for each platform
        const baseUrl = 'http://{local_ip}:{port}';
        
        // Android QR
        QRCode.toCanvas(document.getElementById('android-qr'), baseUrl + '/app-debug.apk', {{
            width: 150,
            height: 150,
            colorDark: '#000000',
            colorLight: '#ffffff',
            margin: 1
        }});
        
        // iOS QR
        QRCode.toCanvas(document.getElementById('ios-qr'), baseUrl + '/ios-install.html', {{
            width: 150,
            height: 150,
            colorDark: '#000000',
            colorLight: '#ffffff',
            margin: 1
        }});
        
        // Web QR
        QRCode.toCanvas(document.getElementById('web-qr'), baseUrl + '/web/', {{
            width: 150,
            height: 150,
            colorDark: '#000000',
            colorLight: '#ffffff',
            margin: 1
        }});
        
        // macOS QR
        QRCode.toCanvas(document.getElementById('macos-qr'), baseUrl + '/macos-install.html', {{
            width: 150,
            height: 150,
            colorDark: '#000000',
            colorLight: '#ffffff',
            margin: 1
        }});
    </script>
</body>
</html>
"""
    
    with open("multi_platform_download.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    return "multi_platform_download.html"

def setup_distribution():
    """Set up distribution files"""
    print("🚀 Setting up multi-platform distribution...")
    
    # Copy web build
    if os.path.exists("build/web"):
        if os.path.exists("web"):
            shutil.rmtree("web")
        shutil.copytree("build/web", "web")
        print("✅ Web app copied")
    
    # Copy Android APK
    if os.path.exists("build/app/outputs/flutter-apk/app-debug.apk"):
        shutil.copy("build/app/outputs/flutter-apk/app-debug.apk", "app-debug.apk")
        print("✅ Android APK copied")
    
    # Create platform-specific instruction pages
    create_ios_install_page()
    create_macos_install_page()
    
    # Create main download page
    create_multi_platform_page()
    print("✅ Multi-platform download page created")

def create_ios_install_page():
    """Create iOS installation instructions"""
    html = """
<!DOCTYPE html>
<html>
<head>
    <title>HIV Predictor - iOS Installation</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body style="font-family: Arial; padding: 20px; background: #f5f5f5;">
    <h1>🍎 iOS Installation</h1>
    <p>iOS installation requires Xcode and Apple Developer account.</p>
    <p>For now, please use the <a href="/web/">Web App version</a> on your iPhone.</p>
    <a href="/" style="background: #007AFF; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">← Back to Downloads</a>
</body>
</html>
"""
    with open("ios-install.html", "w") as f:
        f.write(html)

def create_macos_install_page():
    """Create macOS installation instructions"""
    html = """
<!DOCTYPE html>
<html>
<head>
    <title>HIV Predictor - macOS Installation</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body style="font-family: Arial; padding: 20px; background: #f5f5f5;">
    <h1>🖥️ macOS Installation</h1>
    <p>macOS app requires building on macOS with Xcode.</p>
    <p>For now, please use the <a href="/web/">Web App version</a> on your Mac.</p>
    <a href="/" style="background: #007AFF; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">← Back to Downloads</a>
</body>
</html>
"""
    with open("macos-install.html", "w") as f:
        f.write(html)

if __name__ == "__main__":
    setup_distribution()
    
    PORT = 8080
    local_ip = get_local_ip()
    
    print("=" * 60)
    print("🏥 HIV PREDICTOR - MULTI-PLATFORM DISTRIBUTION")
    print("=" * 60)
    print()
    print(f"📡 Server: http://{local_ip}:{PORT}")
    print(f"🌐 Download Page: http://{local_ip}:{PORT}/multi_platform_download.html")
    print()
    print("📱 Available Platforms:")
    print(f"   • Android APK: http://{local_ip}:{PORT}/app-debug.apk")
    print(f"   • Web App: http://{local_ip}:{PORT}/web/")
    print(f"   • iOS: http://{local_ip}:{PORT}/ios-install.html")
    print(f"   • macOS: http://{local_ip}:{PORT}/macos-install.html")
    print()
    print("🎯 Scan QR codes on the download page for easy installation!")
    print("=" * 60)
    
    # Open download page
    try:
        webbrowser.open(f"http://localhost:{PORT}/multi_platform_download.html")
    except:
        pass
    
    # Start server
    class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            if self.path == "/":
                self.path = "/multi_platform_download.html"
            return super().do_GET()
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")
