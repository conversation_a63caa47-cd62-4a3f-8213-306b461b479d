import 'package:flutter/material.dart';
import '../models/prediction_result.dart';
import '../models/user.dart';

class HealthInsight {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final String category;
  final double confidence;

  HealthInsight({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.category,
    required this.confidence,
  });
}

class HealthInsightsWidget extends StatelessWidget {
  final User user;
  final List<PredictionResult> predictions;

  const HealthInsightsWidget({
    super.key,
    required this.user,
    required this.predictions,
  });

  @override
  Widget build(BuildContext context) {
    final insights = _generateHealthInsights();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF1976D2), Color(0xFF42A5F5)],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.psychology,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'AI Health Insights',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.auto_awesome,
                      size: 12,
                      color: Color(0xFF4CAF50),
                    ),
                    SizedBox(width: 4),
                    Text(
                      'AI Powered',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF4CAF50),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (insights.isEmpty)
            _buildEmptyInsights()
          else
            Column(
              children: insights.map((insight) {
                return _buildInsightCard(insight);
              }).toList(),
            ),
        ],
      ),
    );
  }

  List<HealthInsight> _generateHealthInsights() {
    final insights = <HealthInsight>[];

    if (predictions.isEmpty) {
      insights.add(
        HealthInsight(
          title: 'Start Your Health Journey',
          description:
              'Take your first assessment to receive personalized health insights powered by AI.',
          icon: Icons.rocket_launch,
          color: const Color(0xFF1976D2),
          category: 'Getting Started',
          confidence: 1.0,
        ),
      );
      return insights;
    }

    // Trend Analysis
    if (predictions.length >= 2) {
      final trendInsight = _analyzeTrend();
      if (trendInsight != null) insights.add(trendInsight);
    }

    // Risk Pattern Analysis
    final riskInsight = _analyzeRiskPattern();
    if (riskInsight != null) insights.add(riskInsight);

    // Consistency Analysis
    final consistencyInsight = _analyzeConsistency();
    if (consistencyInsight != null) insights.add(consistencyInsight);

    // Personalized Recommendations
    final recommendationInsight = _generateRecommendation();
    if (recommendationInsight != null) insights.add(recommendationInsight);

    return insights;
  }

  HealthInsight? _analyzeTrend() {
    if (predictions.length < 2) return null;

    final latest = predictions.first.riskLevel.toLowerCase();
    final previous = predictions[1].riskLevel.toLowerCase();

    final latestScore = _getRiskScore(latest);
    final previousScore = _getRiskScore(previous);

    if (latestScore > previousScore) {
      return HealthInsight(
        title: 'Positive Health Trend',
        description:
            'Your health assessments show improvement! Your risk level has decreased compared to previous assessments.',
        icon: Icons.trending_up,
        color: const Color(0xFF4CAF50),
        category: 'Trend Analysis',
        confidence: 0.85,
      );
    } else if (latestScore < previousScore) {
      return HealthInsight(
        title: 'Health Attention Needed',
        description:
            'Recent assessments indicate increased risk. Consider reviewing your health habits and consulting healthcare professionals.',
        icon: Icons.trending_down,
        color: const Color(0xFFFF3547), // New bright red for high risk
        category: 'Trend Analysis',
        confidence: 0.80,
      );
    } else {
      return HealthInsight(
        title: 'Stable Health Pattern',
        description:
            'Your health assessments show consistent results. Maintain your current health practices.',
        icon: Icons.horizontal_rule,
        color: const Color(0xFF2196F3),
        category: 'Trend Analysis',
        confidence: 0.75,
      );
    }
  }

  HealthInsight? _analyzeRiskPattern() {
    final riskCounts = <String, int>{};
    for (final prediction in predictions) {
      final risk = prediction.riskLevel.toLowerCase();
      riskCounts[risk] = (riskCounts[risk] ?? 0) + 1;
    }

    final dominantRisk = riskCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    if (dominantRisk.contains('low')) {
      return HealthInsight(
        title: 'Excellent Health Management',
        description:
            'Most of your assessments show low risk. You\'re doing great at maintaining good health practices!',
        icon: Icons.verified,
        color: const Color(0xFF00C851), // New vibrant green for low risk
        category: 'Risk Analysis',
        confidence: 0.90,
      );
    } else if (dominantRisk.contains('medium')) {
      return HealthInsight(
        title: 'Moderate Risk Pattern',
        description:
            'Your assessments frequently show medium risk. Focus on lifestyle improvements for better health outcomes.',
        icon: Icons.warning_amber,
        color: const Color(0xFFFFB300), // New amber for medium risk
        category: 'Risk Analysis',
        confidence: 0.85,
      );
    } else {
      return HealthInsight(
        title: 'High Risk Alert',
        description:
            'Multiple assessments indicate high risk. Immediate attention to health practices and professional consultation recommended.',
        icon: Icons.error,
        color: const Color(0xFFFF3547), // New bright red for high risk
        category: 'Risk Analysis',
        confidence: 0.95,
      );
    }
  }

  HealthInsight? _analyzeConsistency() {
    if (predictions.length < 3) return null;

    final now = DateTime.now();
    final recentPredictions = predictions.where((p) {
      return now.difference(p.createdAt).inDays <= 30;
    }).toList();

    if (recentPredictions.length >= 7) {
      return HealthInsight(
        title: 'Excellent Tracking Consistency',
        description:
            'You\'ve been consistently tracking your health! This regular monitoring helps identify patterns and improvements.',
        icon: Icons.schedule,
        color: const Color(0xFF4CAF50),
        category: 'Consistency',
        confidence: 0.88,
      );
    } else if (recentPredictions.length >= 3) {
      return HealthInsight(
        title: 'Good Tracking Habit',
        description:
            'You\'re building a good health tracking habit. Try to maintain regular assessments for better insights.',
        icon: Icons.access_time,
        color: const Color(0xFF2196F3),
        category: 'Consistency',
        confidence: 0.75,
      );
    } else {
      return HealthInsight(
        title: 'Improve Tracking Frequency',
        description:
            'More frequent health assessments would provide better insights and help track your progress more effectively.',
        icon: Icons.timer,
        color: const Color(0xFFFF9800),
        category: 'Consistency',
        confidence: 0.70,
      );
    }
  }

  HealthInsight? _generateRecommendation() {
    if (predictions.isEmpty) return null;

    final latestRisk = predictions.first.riskLevel.toLowerCase();

    if (latestRisk.contains('low')) {
      return HealthInsight(
        title: 'Maintain Your Success',
        description:
            'Continue your current health practices. Consider adding preventive measures like regular exercise and balanced nutrition.',
        icon: Icons.fitness_center,
        color: const Color(0xFF4CAF50),
        category: 'Recommendation',
        confidence: 0.80,
      );
    } else if (latestRisk.contains('medium')) {
      return HealthInsight(
        title: 'Focus on Prevention',
        description:
            'Implement lifestyle changes: improve diet, increase physical activity, manage stress, and ensure adequate sleep.',
        icon: Icons.health_and_safety,
        color: const Color(0xFFFF9800),
        category: 'Recommendation',
        confidence: 0.85,
      );
    } else {
      return HealthInsight(
        title: 'Seek Professional Guidance',
        description:
            'Consider consulting healthcare professionals for comprehensive evaluation and personalized treatment plans.',
        icon: Icons.local_hospital,
        color: const Color(0xFFFF3547), // New bright red for high risk
        category: 'Recommendation',
        confidence: 0.90,
      );
    }
  }

  int _getRiskScore(String riskLevel) {
    if (riskLevel.contains('low')) return 3;
    if (riskLevel.contains('medium')) return 2;
    return 1;
  }

  Widget _buildEmptyInsights() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(Icons.psychology_outlined, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'AI Insights Coming Soon',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Complete more assessments to unlock personalized AI-powered health insights',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInsightCard(HealthInsight insight) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: insight.color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: insight.color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: insight.color,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(insight.icon, color: Colors.white, size: 16),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      insight.title,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    Text(
                      insight.category,
                      style: TextStyle(
                        fontSize: 11,
                        color: insight.color,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: insight.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${(insight.confidence * 100).round()}%',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: insight.color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            insight.description,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[700],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}
