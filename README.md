# HIV Predictor - Professional Medical App

## 🏥 About
AI-powered HIV risk assessment application for healthcare providers in Rwanda.

## 🎯 Features
- **AI Risk Assessment**: 22-symptom analysis with machine learning
- **Hospital Finder**: Real Rwanda hospitals with GPS navigation  
- **Admin Dashboard**: Healthcare provider management tools
- **Firebase Integration**: Secure authentication and data storage
- **Multi-Platform**: Android app and web application

## 🚀 Deployment

### Web App (Firebase Hosting)
```bash
flutter build web --release
firebase deploy --only hosting
```

### Android APK
```bash
flutter build apk --release
```

## 📱 Download
- **Web App**: [Live Demo](https://hiv-predictor-app.web.app)
- **Android APK**: Available via QR code or direct download

## 🔧 Technical Stack
- **Frontend**: Flutter 3.32.7
- **Backend**: Firebase (Firestore, Auth, Hosting)
- **AI/ML**: TensorFlow Lite integration
- **Maps**: Google Maps API with real hospital data

## 🏥 Hospital Network
- University Teaching Hospital of Kigali (CHUK)
- King Faisal Hospital
- Rwanda Military Hospital  
- Kibagabaga Hospital
- Muhima Hospital

## 🔐 Security
- Firebase security rules implemented
- User data encryption
- Admin access control
- HIPAA-compliant data handling

## 📊 Version
**Current**: v1.1.0 (Professional Logo Update)
- Professional medical branding
- Enhanced performance
- Updated dependencies
- Clean build process

## 🌍 Impact
Supporting HIV prevention and healthcare access in Rwanda through technology.

---
*Developed for healthcare providers and public health initiatives*
