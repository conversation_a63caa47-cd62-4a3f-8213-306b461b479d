import 'package:flutter/material.dart';
import '../models/prediction_result.dart';

class Achievement {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final int requiredCount;
  final String category;
  final bool isUnlocked;
  final DateTime? unlockedAt;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.requiredCount,
    required this.category,
    this.isUnlocked = false,
    this.unlockedAt,
  });
}

class AchievementSystem {
  static List<Achievement> getAllAchievements(
    List<PredictionResult> predictions,
  ) {
    final assessmentCount = predictions.length;
    final streak = _calculateStreak(predictions);
    final lowRiskCount = predictions
        .where((p) => p.riskLevel.toLowerCase().contains('low'))
        .length;

    return [
      // Assessment Achievements
      Achievement(
        id: 'first_assessment',
        title: 'Health Explorer',
        description: 'Complete your first health assessment',
        icon: Icons.explore,
        color: const Color(0xFF4CAF50),
        requiredCount: 1,
        category: 'Assessment',
        isUnlocked: assessmentCount >= 1,
        unlockedAt: assessmentCount >= 1 ? predictions.first.createdAt : null,
      ),
      Achievement(
        id: 'five_assessments',
        title: 'Health Tracker',
        description: 'Complete 5 health assessments',
        icon: Icons.track_changes,
        color: const Color(0xFF2196F3),
        requiredCount: 5,
        category: 'Assessment',
        isUnlocked: assessmentCount >= 5,
        unlockedAt: assessmentCount >= 5 ? predictions[4].createdAt : null,
      ),
      Achievement(
        id: 'ten_assessments',
        title: 'Health Enthusiast',
        description: 'Complete 10 health assessments',
        icon: Icons.favorite,
        color: const Color(0xFFE91E63),
        requiredCount: 10,
        category: 'Assessment',
        isUnlocked: assessmentCount >= 10,
        unlockedAt: assessmentCount >= 10 ? predictions[9].createdAt : null,
      ),

      // Streak Achievements
      Achievement(
        id: 'three_day_streak',
        title: 'Consistency Starter',
        description: 'Maintain a 3-day health tracking streak',
        icon: Icons.local_fire_department,
        color: const Color(0xFFFF5722),
        requiredCount: 3,
        category: 'Streak',
        isUnlocked: streak >= 3,
      ),
      Achievement(
        id: 'week_streak',
        title: 'Weekly Warrior',
        description: 'Maintain a 7-day health tracking streak',
        icon: Icons.whatshot,
        color: const Color(0xFFFF9800),
        requiredCount: 7,
        category: 'Streak',
        isUnlocked: streak >= 7,
      ),
      Achievement(
        id: 'month_streak',
        title: 'Health Champion',
        description: 'Maintain a 30-day health tracking streak',
        icon: Icons.emoji_events,
        color: const Color(0xFFFFD700),
        requiredCount: 30,
        category: 'Streak',
        isUnlocked: streak >= 30,
      ),

      // Health Achievements
      Achievement(
        id: 'low_risk_master',
        title: 'Health Guardian',
        description: 'Achieve low risk status 5 times',
        icon: Icons.shield,
        color: const Color(0xFF4CAF50),
        requiredCount: 5,
        category: 'Health',
        isUnlocked: lowRiskCount >= 5,
      ),
      Achievement(
        id: 'health_improver',
        title: 'Health Improver',
        description: 'Show improvement in health assessments',
        icon: Icons.trending_up,
        color: const Color(0xFF00BCD4),
        requiredCount: 1,
        category: 'Health',
        isUnlocked: _hasImprovement(predictions),
      ),
    ];
  }

  static int _calculateStreak(List<PredictionResult> predictions) {
    if (predictions.isEmpty) return 0;

    final now = DateTime.now();
    int streak = 0;

    for (final prediction in predictions) {
      final daysDiff = now.difference(prediction.createdAt).inDays;
      if (daysDiff <= streak + 1) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  static bool _hasImprovement(List<PredictionResult> predictions) {
    if (predictions.length < 2) return false;

    final latest = predictions.first.riskLevel.toLowerCase();
    final previous = predictions[1].riskLevel.toLowerCase();

    final latestScore = _getRiskScore(latest);
    final previousScore = _getRiskScore(previous);

    return latestScore > previousScore;
  }

  static int _getRiskScore(String riskLevel) {
    if (riskLevel.contains('low')) return 3;
    if (riskLevel.contains('medium')) return 2;
    return 1;
  }
}

class AchievementWidget extends StatelessWidget {
  final List<PredictionResult> predictions;

  const AchievementWidget({super.key, required this.predictions});

  @override
  Widget build(BuildContext context) {
    final achievements = AchievementSystem.getAllAchievements(predictions);
    final unlockedAchievements = achievements
        .where((a) => a.isUnlocked)
        .toList();
    final recentAchievements = unlockedAchievements.take(3).toList();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.emoji_events,
                color: Color(0xFFFFD700),
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                'Achievements',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFD700).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${unlockedAchievements.length}/${achievements.length}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFFFD700),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (recentAchievements.isEmpty)
            _buildEmptyAchievements()
          else
            Column(
              children: recentAchievements.map((achievement) {
                return _buildAchievementItem(achievement);
              }).toList(),
            ),
          const SizedBox(height: 12),
          GestureDetector(
            onTap: () => _showAllAchievements(context, achievements),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF1976D2).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.view_list, color: Color(0xFF1976D2), size: 16),
                  SizedBox(width: 8),
                  Text(
                    'View All Achievements',
                    style: TextStyle(
                      color: Color(0xFF1976D2),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAchievements() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(Icons.emoji_events_outlined, size: 32, color: Colors.grey[400]),
          const SizedBox(height: 8),
          Text(
            'No achievements yet',
            style: TextStyle(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'Complete assessments to unlock achievements',
            style: TextStyle(color: Colors.grey[500], fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementItem(Achievement achievement) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: achievement.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: achievement.color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: achievement.color,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(achievement.icon, color: Colors.white, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement.title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  achievement.description,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Icon(Icons.check_circle, color: achievement.color, size: 20),
        ],
      ),
    );
  }

  void _showAllAchievements(
    BuildContext context,
    List<Achievement> achievements,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'All Achievements',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    itemCount: achievements.length,
                    itemBuilder: (context, index) {
                      final achievement = achievements[index];
                      return _buildDetailedAchievementItem(achievement);
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildDetailedAchievementItem(Achievement achievement) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: achievement.isUnlocked
            ? achievement.color.withValues(alpha: 0.1)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: achievement.isUnlocked
              ? achievement.color.withValues(alpha: 0.2)
              : Colors.grey[200]!,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: achievement.isUnlocked
                  ? achievement.color
                  : Colors.grey[400],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(achievement.icon, color: Colors.white, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: achievement.isUnlocked
                        ? const Color(0xFF2C3E50)
                        : Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  achievement.description,
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
                const SizedBox(height: 4),
                Text(
                  'Category: ${achievement.category}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            achievement.isUnlocked ? Icons.check_circle : Icons.lock,
            color: achievement.isUnlocked
                ? achievement.color
                : Colors.grey[400],
            size: 24,
          ),
        ],
      ),
    );
  }
}
