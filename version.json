{"version": "1.1.0", "buildNumber": 2, "downloadUrl": "http://172.30.18.51:8080/app-debug.apk", "webUrl": "http://172.30.18.51:8080/web/", "forceUpdate": false, "releaseNotes": "🎨 Professional Logo Update + Performance Improvements!\n\n✨ NEW in v1.1.0:\n• Professional medical app logo with blue gradient\n• HIV awareness ribbon and AI indicator\n• Updated to Flutter 3.32.7 for better performance\n• Fixed Android Gradle Plugin warnings\n• Updated Firebase and dependencies\n• Cleaner build process\n\n✅ Features:\n• AI-powered HIV risk assessment\n• Real Rwanda hospital finder with GPS\n• Secure Firebase authentication\n• Educational content about HIV\n• Admin dashboard for healthcare providers\n• English-only interface\n• Multi-platform support (Android, Web, iOS)\n\n🗺️ Hospital Finder:\n• University Teaching Hospital of Kigali (CHUK)\n• King Faisal Hospital\n• Rwanda Military Hospital\n• Kibagabaga Hospital\n• Muhima Hospital\n\n🔐 Security:\n• Firebase security rules implemented\n• User data protection\n• Admin access control\n• Encrypted data transmission", "minimumSupportedVersion": "1.0.0", "minimumSupportedBuildNumber": 1, "updateCheckInterval": 86400, "platforms": {"android": {"available": true, "downloadUrl": "http://172.30.18.51:8080/app-debug.apk", "fileSize": "15-20 MB", "requirements": "Android 6.0+ (API 23)"}, "web": {"available": true, "url": "http://172.30.18.51:8080/web/", "requirements": "Modern web browser"}, "ios": {"available": false, "reason": "Use web version on iOS devices", "webFallback": "http://172.30.18.51:8080/web/"}, "macos": {"available": false, "reason": "Use web version on macOS", "webFallback": "http://172.30.18.51:8080/web/"}}, "changelog": [{"version": "1.1.0", "buildNumber": 2, "date": "2025-01-16", "changes": ["Professional medical app logo with blue gradient design", "HIV awareness ribbon and AI indicator in logo", "Updated to Flutter 3.32.7 for better performance", "Fixed Android Gradle Plugin warnings", "Updated Firebase BOM to 33.7.0", "Updated Google Services to 4.4.0", "Cleaner build process with fewer warnings", "Enhanced app icon for all Android densities", "Improved professional appearance"]}, {"version": "1.0.0", "buildNumber": 1, "date": "2025-01-16", "changes": ["Initial release", "AI-powered HIV risk assessment", "Hospital finder with real Rwanda hospitals", "Firebase authentication and database", "Admin dashboard functionality", "Multi-platform support", "Location-based hospital navigation"]}], "serverInfo": {"lastUpdated": "2025-01-16T13:00:00Z", "maintenanceMode": false, "supportEmail": "<EMAIL>", "websiteUrl": "http://172.30.18.51:8080/multi_platform_download.html"}}