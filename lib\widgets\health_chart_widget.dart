import 'package:flutter/material.dart';
import '../models/prediction_result.dart';

class HealthChartWidget extends StatelessWidget {
  final List<PredictionResult> predictions;
  final String title;

  const HealthChartWidget({
    super.key,
    required this.predictions,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.trending_up, color: Color(0xFF1976D2), size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (predictions.isEmpty) _buildEmptyChart() else _buildChart(),
        ],
      ),
    );
  }

  Widget _buildEmptyChart() {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.show_chart, size: 32, color: Colors.grey[400]),
          const SizedBox(height: 8),
          Text(
            'No data available',
            style: TextStyle(color: Colors.grey[500], fontSize: 14),
          ),
          Text(
            'Take assessments to see trends',
            style: TextStyle(color: Colors.grey[400], fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildChart() {
    final chartData = _prepareChartData();

    return SizedBox(
      height: 120,
      width: double.infinity,
      child: CustomPaint(painter: HealthChartPainter(chartData)),
    );
  }

  List<ChartPoint> _prepareChartData() {
    final recentPredictions = predictions.take(7).toList().reversed.toList();
    final points = <ChartPoint>[];

    for (int i = 0; i < recentPredictions.length; i++) {
      final prediction = recentPredictions[i];
      final score = _getScoreFromRisk(prediction.riskLevel);
      points.add(ChartPoint(i.toDouble(), score));
    }

    return points;
  }

  double _getScoreFromRisk(String riskLevel) {
    final level = riskLevel.toLowerCase();
    if (level.contains('low')) return 90.0;
    if (level.contains('medium')) return 70.0;
    return 40.0;
  }
}

class ChartPoint {
  final double x;
  final double y;

  ChartPoint(this.x, this.y);
}

class HealthChartPainter extends CustomPainter {
  final List<ChartPoint> points;

  HealthChartPainter(this.points);

  @override
  void paint(Canvas canvas, Size size) {
    if (points.isEmpty) return;

    final paint = Paint()
      ..color = const Color(0xFF1976D2)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final fillPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          const Color(0xFF1976D2).withValues(alpha: 0.3),
          const Color(0xFF1976D2).withValues(alpha: 0.05),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    final path = Path();
    final fillPath = Path();

    // Calculate scaling
    final maxX = points.length > 1 ? (points.length - 1).toDouble() : 1.0;
    const maxY = 100.0;
    const minY = 0.0;

    // Start paths
    final firstPoint = _getScaledPoint(points.first, size, maxX, maxY, minY);
    path.moveTo(firstPoint.dx, firstPoint.dy);
    fillPath.moveTo(firstPoint.dx, size.height);
    fillPath.lineTo(firstPoint.dx, firstPoint.dy);

    // Draw line and fill
    for (int i = 1; i < points.length; i++) {
      final point = _getScaledPoint(points[i], size, maxX, maxY, minY);
      path.lineTo(point.dx, point.dy);
      fillPath.lineTo(point.dx, point.dy);
    }

    // Complete fill path
    if (points.isNotEmpty) {
      final lastPoint = _getScaledPoint(points.last, size, maxX, maxY, minY);
      fillPath.lineTo(lastPoint.dx, size.height);
      fillPath.close();
    }

    // Draw fill and line
    canvas.drawPath(fillPath, fillPaint);
    canvas.drawPath(path, paint);

    // Draw points
    final pointPaint = Paint()
      ..color = const Color(0xFF1976D2)
      ..style = PaintingStyle.fill;

    for (final point in points) {
      final scaledPoint = _getScaledPoint(point, size, maxX, maxY, minY);
      canvas.drawCircle(scaledPoint, 4, pointPaint);

      // Draw white center
      final centerPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;
      canvas.drawCircle(scaledPoint, 2, centerPaint);
    }
  }

  Offset _getScaledPoint(
    ChartPoint point,
    Size size,
    double maxX,
    double maxY,
    double minY,
  ) {
    final x = (point.x / maxX) * size.width;
    final y = size.height - ((point.y - minY) / (maxY - minY)) * size.height;
    return Offset(x, y);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class HealthProgressWidget extends StatelessWidget {
  final double progress;
  final String label;
  final Color color;

  const HealthProgressWidget({
    super.key,
    required this.progress,
    required this.label,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2C3E50),
                ),
              ),
              Text(
                '${(progress * 100).round()}%',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: color.withValues(alpha: 0.1),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      ),
    );
  }
}
