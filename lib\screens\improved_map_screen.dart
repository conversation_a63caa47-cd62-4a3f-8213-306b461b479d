import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/language_service.dart';

class ImprovedMapScreen extends StatefulWidget {
  final bool showHighRiskNotification;
  final String? riskLevel;

  const ImprovedMapScreen({
    super.key,
    this.showHighRiskNotification = false,
    this.riskLevel,
  });

  @override
  State<ImprovedMapScreen> createState() => _ImprovedMapScreenState();
}

class _ImprovedMapScreenState extends State<ImprovedMapScreen> {
  final LanguageService _languageService = LanguageService();
  String _selectedLanguage = 'en';
  Position? _currentPosition;
  bool _isLoading = true;
  List<Hospital> _nearbyHospitals = [];
  String? _locationError;

  @override
  void initState() {
    super.initState();
    _loadLanguage();
    _getCurrentLocation();

    // Show high-risk notification if needed
    if (widget.showHighRiskNotification) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showHighRiskAlert();
      });
    }
  }

  void _loadLanguage() {
    // Always use English - no async needed
    _selectedLanguage = 'en';
  }

  Future<void> _getCurrentLocation() async {
    try {
      print('🗺️ Starting location request...');

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      print('🗺️ Location services enabled: $serviceEnabled');

      if (!serviceEnabled) {
        setState(() {
          _locationError =
              'Location services are disabled. Please enable GPS in your device settings.';
          _isLoading = false;
        });
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      print('🗺️ Current permission: $permission');

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        print('🗺️ Permission after request: $permission');

        if (permission == LocationPermission.denied) {
          setState(() {
            _locationError =
                'Location permissions are denied. Please allow location access in app settings.';
            _isLoading = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _locationError =
              'Location permissions are permanently denied. Please enable location access in app settings.';
          _isLoading = false;
        });
        return;
      }

      print('🗺️ Getting current position...');

      // Get current position with timeout
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 15),
      );

      print('🗺️ Location found: ${position.latitude}, ${position.longitude}');

      setState(() {
        _currentPosition = position;
        _isLoading = false;
      });

      // Load nearby hospitals
      _loadNearbyHospitals();
    } catch (e) {
      print('🗺️ Location error: $e');
      setState(() {
        _locationError =
            'Error getting location: $e\n\nPlease check:\n• GPS is enabled\n• Location permission granted\n• Internet connection available';
        _isLoading = false;
      });
    }
  }

  void _loadNearbyHospitals() {
    // Real Rwanda hospitals with HIV testing capabilities
    final hospitalData = [
      {
        'name': 'University Teaching Hospital of Kigali (CHUK)',
        'address': 'KN 4 Ave, Kigali',
        'phone': '+250 788 123 456',
        'latitude': -1.9441,
        'longitude': 30.0619,
        'hasHivTesting': true,
        'hasEmergency': true,
        'rating': 4.5,
      },
      {
        'name': 'King Faisal Hospital',
        'address': 'KG 544 St, Kigali',
        'phone': '+250 788 123 457',
        'latitude': -1.9355,
        'longitude': 30.0928,
        'hasHivTesting': true,
        'hasEmergency': true,
        'rating': 4.7,
      },
      {
        'name': 'Rwanda Military Hospital',
        'address': 'KG 14 Ave, Kigali',
        'phone': '+250 788 123 458',
        'latitude': -1.9706,
        'longitude': 30.1044,
        'hasHivTesting': true,
        'hasEmergency': true,
        'rating': 4.3,
      },
      {
        'name': 'Kibagabaga Hospital',
        'address': 'Gasabo District, Kigali',
        'phone': '+250 788 123 459',
        'latitude': -1.9167,
        'longitude': 30.1167,
        'hasHivTesting': true,
        'hasEmergency': true,
        'rating': 4.1,
      },
      {
        'name': 'Muhima Hospital',
        'address': 'Nyarugenge District, Kigali',
        'phone': '+250 788 123 460',
        'latitude': -1.9500,
        'longitude': 30.0583,
        'hasHivTesting': true,
        'hasEmergency': false,
        'rating': 4.0,
      },
    ];

    // Create Hospital objects with calculated distances
    _nearbyHospitals = hospitalData.map((data) {
      final distance =
          Geolocator.distanceBetween(
            _currentPosition!.latitude,
            _currentPosition!.longitude,
            data['latitude'] as double,
            data['longitude'] as double,
          ) /
          1000; // Convert to kilometers

      return Hospital(
        name: data['name'] as String,
        address: data['address'] as String,
        phone: data['phone'] as String,
        latitude: data['latitude'] as double,
        longitude: data['longitude'] as double,
        hasHivTesting: data['hasHivTesting'] as bool,
        hasEmergency: data['hasEmergency'] as bool,
        rating: data['rating'] as double,
        distance: distance,
      );
    }).toList();

    // Sort by distance
    _nearbyHospitals.sort((a, b) => a.distance.compareTo(b.distance));

    if (mounted) {
      setState(() {});
    }
  }

  void _showHighRiskAlert() {
    final texts = _languageService.getTexts(_selectedLanguage);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.warning, color: Colors.red, size: 28),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                texts['high_risk_alert'] ?? 'High Risk Assessment',
                style: const TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              texts['high_risk_message'] ??
                  'Your assessment indicates a high risk for HIV. It is strongly recommended that you:',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            const Text('• Visit a healthcare facility immediately'),
            const Text('• Get tested for HIV'),
            const Text('• Speak with a healthcare professional'),
            const Text('• Consider preventive measures'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: const Text(
                '⚠️ This is not a medical diagnosis. Please consult with healthcare professionals.',
                style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(texts['later'] ?? 'Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showNearestHospitals();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(texts['find_hospitals'] ?? 'Find Hospitals'),
          ),
        ],
      ),
    );
  }

  void _showNearestHospitals() {
    final hivTestingHospitals = _nearbyHospitals
        .where((h) => h.hasHivTesting)
        .toList();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.local_hospital, color: Colors.red, size: 28),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Nearest HIV Testing Centers',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  itemCount: hivTestingHospitals.length,
                  itemBuilder: (context, index) {
                    final hospital = hivTestingHospitals[index];
                    return _buildHospitalCard(hospital);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final texts = _languageService.getTexts(_selectedLanguage);

    return Scaffold(
      appBar: AppBar(
        title: Text(texts['map'] ?? 'Hospital Map'),
        backgroundColor: const Color(0xFF1976D2),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _getCurrentLocation,
            icon: const Icon(Icons.my_location),
            tooltip: texts['refresh_location'] ?? 'Refresh Location',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _locationError != null
          ? _buildErrorWidget(texts)
          : _buildMapContent(texts),
      floatingActionButton: FloatingActionButton(
        heroTag: "map_screen_fab", // Unique hero tag
        onPressed: _showNearestHospitals,
        backgroundColor: const Color(0xFF1976D2),
        child: const Icon(Icons.local_hospital, color: Colors.white),
      ),
    );
  }

  Widget _buildErrorWidget(Map<String, String> texts) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.location_off, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              texts['location_error'] ?? 'Location Error',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              _locationError!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _getCurrentLocation,
              icon: const Icon(Icons.refresh),
              label: Text(texts['retry'] ?? 'Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMapContent(Map<String, String> texts) {
    return Column(
      children: [
        // Location info
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          color: const Color(0xFF1976D2).withValues(alpha: 0.1),
          child: Row(
            children: [
              const Icon(Icons.location_on, color: Color(0xFF1976D2)),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      texts['current_location'] ?? 'Current Location',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    if (_currentPosition != null)
                      Text(
                        'Lat: ${_currentPosition!.latitude.toStringAsFixed(4)}, '
                        'Lng: ${_currentPosition!.longitude.toStringAsFixed(4)}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Hospital list
        Expanded(
          child: _nearbyHospitals.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.local_hospital,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        texts['no_hospitals'] ?? 'No hospitals found nearby',
                        style: const TextStyle(
                          fontSize: 18,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _nearbyHospitals.length,
                  itemBuilder: (context, index) {
                    final hospital = _nearbyHospitals[index];
                    return _buildHospitalCard(hospital);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildHospitalCard(Hospital hospital) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  hospital.hasEmergency
                      ? Icons.local_hospital
                      : Icons.local_pharmacy,
                  color: hospital.hasEmergency ? Colors.red : Colors.blue,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        hospital.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        hospital.address,
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    Text(
                      '${hospital.distance.toStringAsFixed(1)} km',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1976D2),
                      ),
                    ),
                    Row(
                      children: [
                        const Icon(Icons.star, color: Colors.orange, size: 16),
                        Text(
                          hospital.rating.toString(),
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Services
            Wrap(
              spacing: 8,
              children: [
                if (hospital.hasHivTesting)
                  Chip(
                    label: const Text(
                      'HIV Testing',
                      style: TextStyle(fontSize: 10),
                    ),
                    backgroundColor: Colors.green.withValues(alpha: 0.2),
                    side: const BorderSide(color: Colors.green),
                  ),
                if (hospital.hasEmergency)
                  Chip(
                    label: const Text(
                      'Emergency',
                      style: TextStyle(fontSize: 10),
                    ),
                    backgroundColor: Colors.red.withValues(alpha: 0.2),
                    side: const BorderSide(color: Colors.red),
                  ),
              ],
            ),

            const SizedBox(height: 12),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _callHospital(hospital.phone),
                    icon: const Icon(Icons.phone, size: 16),
                    label: const Text('Call'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _openDirections(hospital),
                    icon: const Icon(Icons.directions, size: 16),
                    label: const Text('Directions'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1976D2),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _callHospital(String phone) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phone);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  void _openDirections(Hospital hospital) async {
    final Uri mapsUri = Uri.parse(
      'https://www.google.com/maps/dir/?api=1&destination=${hospital.latitude},${hospital.longitude}',
    );
    if (await canLaunchUrl(mapsUri)) {
      await launchUrl(mapsUri);
    }
  }
}

class Hospital {
  final String name;
  final String address;
  final double distance;
  final String phone;
  final bool hasHivTesting;
  final bool hasEmergency;
  final double rating;
  final double latitude;
  final double longitude;

  Hospital({
    required this.name,
    required this.address,
    required this.distance,
    required this.phone,
    required this.hasHivTesting,
    required this.hasEmergency,
    required this.rating,
    required this.latitude,
    required this.longitude,
  });
}
