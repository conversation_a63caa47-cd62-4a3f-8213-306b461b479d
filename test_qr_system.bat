@echo off
title HIV Predictor - QR System Test
color 0B

echo.
echo ========================================
echo   🧪 Testing QR Installation System
echo ========================================
echo.

echo 🔧 Creating test environment...

REM Create a dummy APK file for testing
echo Creating test APK file...
mkdir build\app\outputs\flutter-apk 2>nul
echo This is a test APK file for QR system testing > build\app\outputs\flutter-apk\app-debug.apk

echo ✅ Test APK created
echo.

echo 🌐 Starting QR Test Server...
echo.

REM Get local IP
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set LOCAL_IP=%%a
    goto :found_ip
)
:found_ip
set LOCAL_IP=%LOCAL_IP: =%

echo 📡 Test Server Details:
echo    • Local IP: %LOCAL_IP%
echo    • Port: 8080
echo    • QR Page: http://%LOCAL_IP%:8080/qr_download_page.html
echo.

echo 📱 QR System Test Instructions:
echo    1. QR page will open in your browser
echo    2. Scan the QR code with your phone
echo    3. Verify the download link works
echo    4. Test the installation flow
echo.

echo 🚀 Starting test server...
echo    Press Ctrl+C when done testing
echo.

REM Copy files for serving
copy "build\app\outputs\flutter-apk\app-debug.apk" "app-debug.apk" >nul 2>&1

REM Open QR page
start http://localhost:8080/qr_download_page.html

REM Start server
python -m http.server 8080

echo.
echo 🛑 Test server stopped.
pause
