// Firebase Admin Setup Script
// Run this in Firebase Console > Firestore > Data or use Firebase CLI

// 1. First, create these collections and documents in Firestore:

// Collection: admins
// Document ID: YOUR_FIREBASE_AUTH_USER_ID
const adminUserExample = {
  email: "<EMAIL>",
  role: "admin", 
  createdAt: new Date(),
  permissions: [
    "read_all_users",
    "read_all_predictions", 
    "manage_notifications",
    "view_analytics",
    "manage_settings"
  ],
  isActive: true,
  lastLogin: null
};

// Collection: settings
// Document ID: app_config
const appSettings = {
  appName: "HIV Predictor",
  version: "1.0.0",
  maintenanceMode: false,
  allowRegistration: true,
  requireEmailVerification: true,
  maxPredictionsPerDay: 10,
  createdAt: new Date(),
  updatedAt: new Date()
};

// Collection: config
// Document ID: security_config  
const securityConfig = {
  sessionTimeout: 3600, // 1 hour in seconds
  maxLoginAttempts: 5,
  passwordMinLength: 8,
  requireStrongPassword: true,
  enableTwoFactor: false,
  createdAt: new Date()
};

// Collection: education
// Document ID: hiv_basics
const educationContent = {
  title: "HIV Basics",
  content: "Human Immunodeficiency Virus (HIV) is a virus that attacks the immune system...",
  category: "basics",
  isPublic: true,
  language: "en",
  createdAt: new Date(),
  updatedAt: new Date(),
  author: "HIV Predictor Team"
};

// Instructions for manual setup:
console.log(`
🔥 FIREBASE ADMIN SETUP INSTRUCTIONS

1. Go to Firebase Console: https://console.firebase.google.com/
2. Select project: hiv-predictor-app
3. Navigate to Firestore Database > Data

4. CREATE ADMIN USER:
   - Collection: admins
   - Document ID: [YOUR_FIREBASE_AUTH_USER_ID]
   - Fields: Copy from adminUserExample above

5. CREATE APP SETTINGS:
   - Collection: settings  
   - Document ID: app_config
   - Fields: Copy from appSettings above

6. CREATE SECURITY CONFIG:
   - Collection: config
   - Document ID: security_config
   - Fields: Copy from securityConfig above

7. CREATE EDUCATION CONTENT:
   - Collection: education
   - Document ID: hiv_basics
   - Fields: Copy from educationContent above

8. APPLY SECURITY RULES:
   - Go to Firestore > Rules
   - Copy content from firestore_security_rules.rules
   - Click Publish

⚠️  IMPORTANT: Replace YOUR_FIREBASE_AUTH_USER_ID with your actual Firebase Auth user ID!

To find your Firebase Auth User ID:
1. Go to Firebase Console > Authentication > Users
2. Find your email and copy the User UID
3. Use this UID as the document ID in the admins collection
`);

// Alternative: Firebase CLI commands (if you have Firebase CLI installed)
const firebaseCliCommands = `
# Install Firebase CLI (if not installed)
npm install -g firebase-tools

# Login to Firebase
firebase login

# Set project
firebase use hiv-predictor-app

# Deploy security rules
firebase deploy --only firestore:rules

# Add admin user via Firebase Admin SDK (requires setup)
# This would be done in a Node.js script with admin privileges
`;

console.log("Firebase CLI Commands:", firebaseCliCommands);
