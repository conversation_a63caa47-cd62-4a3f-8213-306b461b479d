import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';
import '../models/user.dart' as app_user;

class FirebaseAuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Convert Firebase User to App User
  app_user.User? _userFromFirebaseUser(User? user, {String? username}) {
    if (user == null) return null;

    return app_user.User(
      id: user.uid,
      username: username ?? user.email?.split('@').first ?? 'user',
      email: user.email ?? '',
      fullName: user.displayName ?? 'User',
      createdAt: user.metadata.creationTime ?? DateTime.now(),
    );
  }

  // Get current app user
  Future<app_user.User?> getCurrentUser() async {
    final user = currentUser;
    if (user == null) return null;

    try {
      // Get additional user data from Firestore
      final doc = await _firestore.collection('users').doc(user.uid).get();
      if (doc.exists) {
        final data = doc.data()!;
        return app_user.User(
          id: user.uid,
          username: data['username'] ?? user.email?.split('@').first ?? 'user',
          email: user.email ?? '',
          fullName: data['fullName'] ?? user.displayName ?? 'User',
          createdAt:
              (data['createdAt'] as Timestamp?)?.toDate() ??
              user.metadata.creationTime ??
              DateTime.now(),
          isAdmin: data['isAdmin'] ?? false, // Add isAdmin field
        );
      }
    } catch (e) {
      _logger.e('Error getting user data: $e');
    }

    return _userFromFirebaseUser(user);
  }

  // Sign up with username, email and password
  Future<app_user.User?> signUpWithUsernameEmailAndPassword({
    required String username,
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      // Check if username already exists
      final usernameQuery = await _firestore
          .collection('users')
          .where('username', isEqualTo: username.toLowerCase())
          .get();

      if (usernameQuery.docs.isNotEmpty) {
        throw Exception(
          'Username already exists. Please choose a different username.',
        );
      }

      // Create user account with timeout
      final credential = await _auth
          .createUserWithEmailAndPassword(email: email, password: password)
          .timeout(const Duration(seconds: 15));

      final user = credential.user;
      if (user != null) {
        // Perform operations in parallel for better performance
        await Future.wait([
          // Update display name
          user.updateDisplayName(fullName).timeout(const Duration(seconds: 10)),

          // Save additional user data to Firestore
          _firestore
              .collection('users')
              .doc(user.uid)
              .set({
                'username': username.toLowerCase(),
                'email': email,
                'fullName': fullName,
                'createdAt': FieldValue.serverTimestamp(),
                'lastLoginAt': FieldValue.serverTimestamp(),
                'isAdmin': false,
              })
              .timeout(const Duration(seconds: 10)),
        ]);

        _logger.i('User account created successfully: ${user.uid}');

        return app_user.User(
          id: user.uid,
          username: username.toLowerCase(),
          email: email,
          fullName: fullName,
          createdAt: DateTime.now(),
        );
      }
    } catch (e) {
      _logger.e('Sign up error: $e');

      // Provide more specific error messages
      if (e.toString().contains('email-already-in-use')) {
        throw Exception(
          'This email address is already registered. Please use a different email or try signing in.',
        );
      } else if (e.toString().contains('weak-password')) {
        throw Exception(
          'Password is too weak. Please use at least 6 characters with a mix of letters and numbers.',
        );
      } else if (e.toString().contains('invalid-email')) {
        throw Exception('Please enter a valid email address.');
      } else if (e.toString().contains('TimeoutException')) {
        throw Exception(
          'Registration is taking longer than expected. Please check your internet connection and try again.',
        );
      }

      rethrow;
    }
    return null;
  }

  // Create admin user (call this once to set up admin)
  Future<void> createAdminUser() async {
    try {
      // Check if admin already exists
      final adminQuery = await _firestore
          .collection('users')
          .where('username', isEqualTo: 'admin')
          .get();

      if (adminQuery.docs.isNotEmpty) {
        _logger.i('Admin user already exists');
        return;
      }

      // Create admin user in Firebase Auth
      final credential = await _auth.createUserWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'admin123',
      );

      final user = credential.user;
      if (user != null) {
        // Create admin user document in Firestore
        await _firestore.collection('users').doc(user.uid).set({
          'username': 'admin',
          'email': '<EMAIL>',
          'fullName': 'System Administrator',
          'createdAt': FieldValue.serverTimestamp(),
          'lastLoginAt': FieldValue.serverTimestamp(),
          'isAdmin': true, // This is the key difference
        });

        _logger.i('Admin user created successfully');
      }
    } catch (e) {
      _logger.e('Error creating admin user: $e');
      rethrow;
    }
  }

  // Create test user for debugging
  Future<void> createTestUser() async {
    try {
      // Check if test user already exists
      final testQuery = await _firestore
          .collection('users')
          .where('username', isEqualTo: 'testuser')
          .get();

      if (testQuery.docs.isNotEmpty) {
        _logger.i('Test user already exists');
        return;
      }

      // Create test user in Firebase Auth
      final credential = await _auth.createUserWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'test123',
      );

      final user = credential.user;
      if (user != null) {
        // Save test user data to Firestore
        await _firestore.collection('users').doc(user.uid).set({
          'username': 'testuser',
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'createdAt': FieldValue.serverTimestamp(),
          'lastLoginAt': FieldValue.serverTimestamp(),
          'isAdmin': false,
        });

        _logger.i('Test user created successfully');
      }
    } catch (e) {
      _logger.e('Error creating test user: $e');
    }
  }

  // Sign in with username and password
  Future<app_user.User?> signInWithUsernameAndPassword({
    required String username,
    required String password,
  }) async {
    try {
      _logger.i('Attempting to sign in with username: $username');

      // Add timeout and retry logic for better reliability
      final usernameQuery = await _firestore
          .collection('users')
          .where('username', isEqualTo: username.toLowerCase())
          .get()
          .timeout(
            const Duration(seconds: 15),
            onTimeout: () => throw Exception(
              'Connection timeout. Please check your internet connection and try again.',
            ),
          );

      _logger.i(
        'Username query returned ${usernameQuery.docs.length} documents',
      );

      if (usernameQuery.docs.isEmpty) {
        _logger.w('Username not found: $username');
        throw Exception(
          'Username not found. Please check your username or sign up for a new account.',
        );
      }

      final userData = usernameQuery.docs.first.data();
      final email = userData['email'] as String;

      _logger.i('Found user with email: $email');

      // Now sign in with email and password with timeout
      final credential = await _auth
          .signInWithEmailAndPassword(email: email, password: password)
          .timeout(
            const Duration(seconds: 15),
            onTimeout: () => throw Exception(
              'Login timeout. Please check your internet connection and try again.',
            ),
          );

      final user = credential.user;
      if (user != null) {
        _logger.i('Successfully signed in user: ${user.uid}');

        // Update last login time with error handling
        try {
          await _firestore
              .collection('users')
              .doc(user.uid)
              .update({'lastLoginAt': FieldValue.serverTimestamp()})
              .timeout(const Duration(seconds: 10));
        } catch (updateError) {
          _logger.w('Failed to update last login time: $updateError');
          // Don't fail the login for this
        }

        return await getCurrentUser();
      }
    } catch (e) {
      _logger.e('Sign in error: $e');

      // Provide more specific error messages
      if (e.toString().contains('wrong-password') ||
          e.toString().contains('invalid-credential')) {
        throw Exception(
          'Incorrect password. Please try again or use "Forgot Password" to reset your password.',
        );
      } else if (e.toString().contains('user-not-found')) {
        throw Exception(
          'Username not found. Please check your username or sign up for a new account.',
        );
      } else if (e.toString().contains('too-many-requests')) {
        throw Exception(
          'Too many failed login attempts. Please try again later.',
        );
      } else if (e.toString().contains('network') ||
          e.toString().contains('timeout') ||
          e.toString().contains('Connection timeout')) {
        throw Exception(
          'Network error. Please check your internet connection and try again.',
        );
      } else if (e.toString().contains('firebase_auth/user-disabled')) {
        throw Exception(
          'This account has been disabled. Please contact support.',
        );
      }

      rethrow;
    }
    return null;
  }

  // Sign in with email and password (kept for backward compatibility)
  Future<app_user.User?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = credential.user;
      if (user != null) {
        // Update last login time
        await _firestore.collection('users').doc(user.uid).update({
          'lastLoginAt': FieldValue.serverTimestamp(),
        });

        return await getCurrentUser();
      }
    } catch (e) {
      _logger.e('Sign in error: $e');
      rethrow;
    }
    return null;
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      _logger.e('Sign out error: $e');
      rethrow;
    }
  }

  // Update user email
  Future<void> updateEmail(String newEmail) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      await user.verifyBeforeUpdateEmail(newEmail);
      _logger.i('✅ Email verification sent successfully');
    } catch (e) {
      _logger.e('❌ Error updating email: $e');
      rethrow;
    }
  }

  // Change user password
  Future<void> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      // Re-authenticate user with current password
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );

      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(newPassword);
      _logger.i('✅ Password changed successfully');
    } catch (e) {
      _logger.e('❌ Error changing password: $e');
      rethrow;
    }
  }

  // Reset password by username
  Future<void> resetPasswordByUsername(String username) async {
    try {
      // First, find the user by username to get their email
      final usernameQuery = await _firestore
          .collection('users')
          .where('username', isEqualTo: username.toLowerCase())
          .get();

      if (usernameQuery.docs.isEmpty) {
        throw Exception('Username not found. Please check your username.');
      }

      final userData = usernameQuery.docs.first.data();
      final email = userData['email'] as String;

      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      _logger.e('Reset password error: $e');
      rethrow;
    }
  }

  // Reset password by email (kept for backward compatibility)
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      _logger.e('Reset password error: $e');
      rethrow;
    }
  }

  // Delete account
  Future<void> deleteAccount() async {
    try {
      final user = currentUser;
      if (user != null) {
        // Delete user data from Firestore
        await _firestore.collection('users').doc(user.uid).delete();

        // Delete user predictions
        final predictions = await _firestore
            .collection('predictions')
            .where('userId', isEqualTo: user.uid)
            .get();

        for (final doc in predictions.docs) {
          await doc.reference.delete();
        }

        // Delete Firebase Auth account
        await user.delete();
      }
    } catch (e) {
      _logger.e('Delete account error: $e');
      rethrow;
    }
  }

  // Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      _logger.i('Password reset email sent to: $email');
    } on FirebaseAuthException catch (e) {
      _logger.e('Error sending password reset email: ${e.code} - ${e.message}');

      switch (e.code) {
        case 'user-not-found':
          throw 'No account found with this email address. Please check your email or sign up for a new account.';
        case 'invalid-email':
          throw 'Please enter a valid email address.';
        case 'too-many-requests':
          throw 'Too many password reset attempts. Please try again later.';
        default:
          throw 'Failed to send password reset email. Please try again.';
      }
    } catch (e) {
      _logger.e('Unexpected error sending password reset email: $e');
      throw 'An unexpected error occurred. Please try again.';
    }
  }

  // Check if email exists
  Future<bool> isEmailRegistered(String email) async {
    try {
      // Since fetchSignInMethodsForEmail is deprecated, we'll use a different approach
      // Try to sign in with a dummy password to check if email exists
      await _auth.signInWithEmailAndPassword(
        email: email,
        password: 'dummy_password_check',
      );
      // If we reach here without exception, email exists but password was wrong
      return true;
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found') {
        return false; // Email is not registered
      } else if (e.code == 'wrong-password' || e.code == 'invalid-credential') {
        return true; // Email exists but password is wrong
      }
      _logger.e('Check email error: $e');
      return false;
    } catch (e) {
      _logger.e('Check email error: $e');
      return false;
    }
  }
}
