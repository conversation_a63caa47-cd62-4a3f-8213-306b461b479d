class LanguageService {
  static final LanguageService _instance = LanguageService._internal();
  factory LanguageService() => _instance;
  LanguageService._internal();

  // Get English translations
  Map<String, String> get texts => _englishTranslations;

  // English-only translations
  static const Map<String, String> _englishTranslations = {
    // Landing Screen
    'app_title': 'HIV Predictor',
    'app_subtitle': 'AI-powered HIV risk assessment and healthcare navigation',
    'feature_ai': 'AI-Powered Assessment',
    'feature_ai_desc': 'Advanced algorithms for accurate risk evaluation',
    'feature_location': 'Hospital Finder',
    'feature_location_desc': 'Find nearby healthcare facilities instantly',
    'feature_education': 'HIV Education',
    'feature_education_desc': 'Comprehensive information and resources',
    'get_started': 'Get Started',
    'sign_in': 'Sign In',
    'footer_text': 'Your health, our priority',

    // Navigation
    'predict': 'Predict',
    'education': 'Education',
    'map': 'Map',
    'settings': 'Settings',
    'dashboard': 'Dashboard',
    'home': 'Home',
    'logout': 'Logout',
    'profile': 'Profile',
    'hospitals': 'Hospitals',
    'assessment': 'Assessment',
    'login': 'Login',

    // Education Screen
    'hiv_education': 'HIV Education',
    'search_topics': 'Search topics...',
    'bookmark': 'Bookmark',
    'bookmarked': 'Bookmarked',
    'watch_video': 'Watch Educational Video',
    'read_more': 'Read More',
    'prevention': 'Prevention',
    'symptoms': 'Symptoms',
    'testing': 'Testing',
    'treatment': 'Treatment',
    'support': 'Support',
    'myths_facts': 'Myths & Facts',

    // Settings
    'notifications': 'Notifications',
    'privacy_data': 'Privacy & Data',
    'appearance': 'Appearance',
    'accessibility': 'Accessibility',
    'security': 'Security',
    'emergency': 'Emergency',
    'advanced': 'Advanced',

    // Common
    'yes': 'Yes',
    'no': 'No',
    'sometimes': 'Sometimes',
    'save': 'Save',
    'cancel': 'Cancel',
    'ok': 'OK',
    'error': 'Error',
    'success': 'Success',
    'loading': 'Loading...',

    // Symptoms - Enhanced with all 22 symptoms
    'fever': 'Fever',
    'headache': 'Headache',
    'skin_rash': 'Skin Rash',
    'muscle_pain': 'Muscle Pain',
    'weight_loss': 'Weight Loss',
    'fatigue': 'Fatigue',
    'oral_ulcers': 'Oral Ulcers',
    'swollen_lymph_nodes': 'Swollen Lymph Nodes',
    'diarrhea': 'Diarrhea',
    'night_sweats': 'Night Sweats',

    // New early/acute symptoms
    'sore_throat': 'Sore Throat',
    'joint_pain': 'Joint Pain',
    'nausea': 'Nausea',
    'loss_of_appetite': 'Loss of Appetite',
    'chills': 'Chills',
    'persistent_cough': 'Persistent Cough',

    // New advanced symptoms
    'recurring_infections': 'Recurring Infections',
    'memory_problems': 'Memory Problems',
    'vision_problems': 'Vision Problems',
    'persistent_headaches': 'Persistent Headaches',

    // Risk behaviors
    'unprotected_sex': 'Unprotected Sex',
    'shared_needles': 'Shared Needles',
    'multiple_partners': 'Multiple Partners',
    'blood_transfusion': 'Recent Blood Transfusion',
    'tattoo_piercing_unsterile': 'Unsterile Tattoo/Piercing',
    'partner_hiv_positive': 'Partner with HIV/Unknown Status',

    // Symptom sections
    'common_physical_symptoms': 'Common Physical Symptoms',
    'early_infection_symptoms': 'Early Infection Symptoms',
    'advanced_symptoms': 'Advanced Symptoms',
    'additional_risk_factors': 'Additional Risk Factors',
    'risk_behaviors': 'Risk Behaviors',

    // Hospital finder
    'hospital_map': 'Hospital Map',
    'find_hospitals': 'Find Hospitals',
    'nearest_hospital': 'Nearest Hospital',
    'get_directions': 'Get Directions',
    'call_hospital': 'Call Hospital',
    'location_error': 'Location Error',
    'location_permission_denied': 'Location permission denied',
    'enable_location': 'Enable Location',
    'retry': 'Retry',
    'refresh_location': 'Refresh Location',
    'later': 'Later',
    'current_location': 'Current Location',
    'no_hospitals': 'No hospitals found nearby',
    'call': 'Call',
    'directions': 'Directions',

    // Map and Hospital Features
    'high_risk_alert': 'High Risk Assessment',
    'high_risk_message':
        'Your assessment indicates a high risk for HIV. It is strongly recommended that you:',

    // Home Screen
    'welcome_message': 'Take care of your health today',
    'quick_actions': 'Quick Actions',
    'new_assessment': 'New Assessment',
    'check_risk': 'Check your risk',
    'nearby_care': 'Nearby care',
    'learn_more': 'Learn more',
    'view_results': 'View results',
    'your_stats': 'Your Statistics',
    'total_assessments': 'Total Assessments',
    'last_check': 'Last Check',
    'never': 'Never',
    'recent_activity': 'Recent Activity',
    'view_all': 'View All',
    'no_assessments': 'No assessments yet',
    'health_tips': 'Health Tips',
    'tip_prevention': 'Use protection during sexual activity',
    'tip_testing': 'Get tested regularly if you\'re at risk',
    'tip_health': 'Maintain a healthy lifestyle',

    // Authentication
    'sign_in_to_continue': 'Sign in to access all features',
    'auth_description':
        'Track your assessments, view history, and get personalized recommendations',
    'sign_up': 'Sign Up',
    'available_features': 'Available Features',
    'learn_about_hiv': 'Learn about HIV',
    'locate_healthcare': 'Locate healthcare',
  };

  // Get current language (always English)
  String getCurrentLanguage() {
    return 'en';
  }

  // Initialize service (simplified)
  Future<void> initialize() async {
    // Always English - nothing to initialize
  }

  // Set language (always returns true for English)
  Future<bool> setLanguage(String languageCode) async {
    // Always English - nothing to set
    return true;
  }

  // Get texts for specified language (always returns English)
  Map<String, String> getTexts(String languageCode) {
    return _englishTranslations;
  }

  // Get specific text by key
  String getText(String key, [String? languageCode]) {
    return _englishTranslations[key] ?? key;
  }

  // Get supported languages (only English)
  List<String> getSupportedLanguages() {
    return ['en'];
  }

  // Get language name
  String getLanguageName(String languageCode) {
    return 'English';
  }
}
