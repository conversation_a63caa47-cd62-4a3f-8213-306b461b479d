# Complete Firebase Setup for HIV Predictor App

## 1. Firestore Security Rules (`firebase/firestore.rules`)

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    function isAdmin() {
      return isAuthenticated() &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    function isValidUser(userData) {
      return userData.keys().hasAll(['username', 'email', 'fullName', 'createdAt', 'isAdmin']) &&
             userData.username is string &&
             userData.email is string &&
             userData.fullName is string &&
             userData.createdAt is timestamp &&
             userData.isAdmin is bool;
    }

    function isValidPrediction(predictionData) {
      return predictionData.keys().hasAll(['userId', 'prediction', 'confidence', 'symptoms', 'riskLevel', 'recommendations', 'createdAt']) &&
             predictionData.userId is string &&
             predictionData.prediction is string &&
             predictionData.confidence is number &&
             predictionData.symptoms is map &&
             predictionData.riskLevel is string &&
             predictionData.recommendations is list &&
             predictionData.createdAt is timestamp;
    }

    // Users collection
    match /users/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
      allow read: if isAdmin();
      allow create: if isAuthenticated() && isOwner(userId) && isValidUser(resource.data);
      allow update: if isAuthenticated() && isOwner(userId) && isValidUser(resource.data);
      allow delete: if isAuthenticated() && (isOwner(userId) || isAdmin());
    }

    // Predictions collection
    match /predictions/{predictionId} {
      allow read: if isAuthenticated() && (isOwner(resource.data.userId) || isAdmin());
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId) && isValidPrediction(request.resource.data);
      allow update: if isAuthenticated() && isOwner(resource.data.userId) && isValidPrediction(request.resource.data);
      allow delete: if isAuthenticated() && (isOwner(resource.data.userId) || isAdmin());
    }

    // Symptoms collection (reference data)
    match /symptoms/{symptomId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Hospitals collection (reference data)
    match /hospitals/{hospitalId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Notifications collection
    match /notifications/{notificationId} {
      allow read: if isAuthenticated() && (isOwner(resource.data.userId) || resource.data.isGlobal == true || isAdmin());
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (isOwner(resource.data.userId) || isAdmin());
      allow delete: if isAdmin();
    }

    // Admin logs collection
    match /admin_logs/{logId} {
      allow read: if isAdmin();
      allow create: if isAdmin();
      allow update, delete: if false;
    }

    // Education content collection
    match /education_content/{contentId} {
      allow read: if isAuthenticated() && (resource.data.isPublished == true || isAdmin());
      allow write: if isAdmin();
    }

    // System settings collection
    match /system_settings/{settingId} {
      allow read: if isAuthenticated() && (resource.data.isPublic == true || isAdmin());
      allow write: if isAdmin();
    }

    // Analytics collection
    match /analytics/{eventId} {
      allow create: if isAuthenticated() && (isOwner(request.resource.data.userId) || request.resource.data.userId == null);
      allow read: if isAdmin();
      allow update, delete: if false;
    }

    // Feedback collection
    match /feedback/{feedbackId} {
      allow read: if isAuthenticated() && (isOwner(resource.data.userId) || isAdmin());
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (isOwner(resource.data.userId) || isAdmin());
      allow delete: if isAdmin();
    }

    // Default deny rule
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

## 2. Storage Security Rules (`firebase/storage.rules`)

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {

    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    function isAdmin() {
      return isAuthenticated() &&
             firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    function isValidImageFile() {
      return resource.contentType.matches('image/.*') && resource.size < 5 * 1024 * 1024;
    }

    function isValidDocumentFile() {
      return (resource.contentType.matches('application/pdf') ||
             resource.contentType.matches('text/.*') ||
             resource.contentType.matches('application/.*csv')) &&
             resource.size < 10 * 1024 * 1024;
    }

    // User profile images
    match /users/{userId}/profile/{imageId} {
      allow read, write: if isAuthenticated() && isOwner(userId) && isValidImageFile();
      allow read: if isAdmin();
    }

    // Education content images
    match /education/{contentId}/{imageId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() && isValidImageFile();
    }

    // Data exports (admin only)
    match /exports/{exportId} {
      allow read, write: if isAdmin() && isValidDocumentFile();
    }

    // Temporary uploads
    match /temp/{userId}/{fileId} {
      allow read, write: if isAuthenticated() && isOwner(userId) && (isValidImageFile() || isValidDocumentFile());
    }

    // System backups (admin only)
    match /backups/{backupId} {
      allow read, write: if isAdmin();
    }

    // Default deny rule
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
```

## 3. Firebase Configuration (`firebase/firebase.json`)

```json
{
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "storage": {
    "rules": "storage.rules"
  },
  "hosting": {
    "public": "build/web",
    "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
    "rewrites": [{ "source": "**", "destination": "/index.html" }],
    "headers": [
      {
        "source": "**/*.@(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)",
        "headers": [{ "key": "Cache-Control", "value": "max-age=31536000" }]
      }
    ]
  },
  "functions": {
    "source": "functions",
    "runtime": "nodejs18",
    "predeploy": [
      "npm --prefix \"$RESOURCE_DIR\" run lint",
      "npm --prefix \"$RESOURCE_DIR\" run build"
    ]
  },
  "emulators": {
    "auth": { "port": 9099 },
    "firestore": { "port": 8080 },
    "storage": { "port": 9199 },
    "hosting": { "port": 5000 },
    "functions": { "port": 5001 },
    "ui": { "enabled": true, "port": 4000 },
    "singleProjectMode": true
  }
}
```

## 4. Complete Cloud Functions Code (`firebase/functions/src/index.ts`)

```typescript
import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import * as express from "express";
import * as cors from "cors";
import * as helmet from "helmet";
import { RateLimiterMemory } from "rate-limiter-flexible";
import { Parser } from "json2csv";

// Initialize Firebase Admin
admin.initializeApp();
const db = admin.firestore();
const auth = admin.auth();

// Initialize Express app
const app = express();
app.use(helmet());
app.use(cors({ origin: true }));
app.use(express.json({ limit: "10mb" }));

// Rate limiting
const rateLimiter = new RateLimiterMemory({
  keyGenerator: (req) => req.ip,
  points: 100, // Number of requests
  duration: 60, // Per 60 seconds
});

const rateLimitMiddleware = async (req: any, res: any, next: any) => {
  try {
    await rateLimiter.consume(req.ip);
    next();
  } catch (rejRes) {
    res.status(429).send("Too Many Requests");
  }
};

app.use(rateLimitMiddleware);

// Authentication middleware
const authenticateUser = async (req: any, res: any, next: any) => {
  try {
    const token = req.headers.authorization?.split("Bearer ")[1];
    if (!token) {
      return res.status(401).json({ error: "No token provided" });
    }
    const decodedToken = await auth.verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    res.status(401).json({ error: "Invalid token" });
  }
};

// Admin middleware
const requireAdmin = async (req: any, res: any, next: any) => {
  try {
    const userDoc = await db.collection("users").doc(req.user.uid).get();
    const userData = userDoc.data();
    if (!userData?.isAdmin) {
      return res.status(403).json({ error: "Admin access required" });
    }
    next();
  } catch (error) {
    res.status(500).json({ error: "Error checking admin status" });
  }
};

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
  });
});

// Export user data (Admin only)
app.get(
  "/admin/export/users",
  authenticateUser,
  requireAdmin,
  async (req, res) => {
    try {
      const usersSnapshot = await db.collection("users").get();
      const users = usersSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || null,
        lastLoginAt: doc.data().lastLoginAt?.toDate?.()?.toISOString() || null,
      }));

      const format = (req.query.format as string) || "json";
      if (format === "csv") {
        const parser = new Parser();
        const csv = parser.parse(users);
        res.setHeader("Content-Type", "text/csv");
        res.setHeader("Content-Disposition", "attachment; filename=users.csv");
        res.send(csv);
      } else {
        res.setHeader("Content-Type", "application/json");
        res.setHeader("Content-Disposition", "attachment; filename=users.json");
        res.json(users);
      }

      // Log admin action
      await db.collection("admin_logs").add({
        adminId: req.user.uid,
        action: "export_users",
        targetResource: "users",
        metadata: { format, recordCount: users.length },
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        result: "success",
        description: `Exported ${users.length} users in ${format} format`,
      });
    } catch (error) {
      console.error("Error exporting users:", error);
      res.status(500).json({ error: "Failed to export users" });
    }
  }
);

// Export predictions data (Admin only)
app.get(
  "/admin/export/predictions",
  authenticateUser,
  requireAdmin,
  async (req, res) => {
    try {
      const predictionsSnapshot = await db.collection("predictions").get();
      const usersSnapshot = await db.collection("users").get();

      // Create user lookup map
      const userMap = new Map();
      usersSnapshot.docs.forEach((doc) => {
        userMap.set(doc.id, doc.data());
      });

      const predictions = predictionsSnapshot.docs.map((doc) => {
        const data = doc.data();
        const user = userMap.get(data.userId);
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || null,
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || null,
          username: user?.username || "Unknown",
          userEmail: user?.email || "Unknown",
          userFullName: user?.fullName || "Unknown",
        };
      });

      const format = (req.query.format as string) || "json";
      if (format === "csv") {
        const parser = new Parser();
        const csv = parser.parse(predictions);
        res.setHeader("Content-Type", "text/csv");
        res.setHeader(
          "Content-Disposition",
          "attachment; filename=predictions.csv"
        );
        res.send(csv);
      } else {
        res.setHeader("Content-Type", "application/json");
        res.setHeader(
          "Content-Disposition",
          "attachment; filename=predictions.json"
        );
        res.json(predictions);
      }

      // Log admin action
      await db.collection("admin_logs").add({
        adminId: req.user.uid,
        action: "export_predictions",
        targetResource: "predictions",
        metadata: { format, recordCount: predictions.length },
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        result: "success",
        description: `Exported ${predictions.length} predictions in ${format} format`,
      });
    } catch (error) {
      console.error("Error exporting predictions:", error);
      res.status(500).json({ error: "Failed to export predictions" });
    }
  }
);

// Export all data (Admin only)
app.get(
  "/admin/export/all",
  authenticateUser,
  requireAdmin,
  async (req, res) => {
    try {
      const [
        usersSnapshot,
        predictionsSnapshot,
        symptomsSnapshot,
        hospitalsSnapshot,
      ] = await Promise.all([
        db.collection("users").get(),
        db.collection("predictions").get(),
        db.collection("symptoms").get(),
        db.collection("hospitals").get(),
      ]);

      const exportData = {
        users: usersSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || null,
          lastLoginAt:
            doc.data().lastLoginAt?.toDate?.()?.toISOString() || null,
        })),
        predictions: predictionsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || null,
          updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || null,
        })),
        symptoms: symptomsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })),
        hospitals: hospitalsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })),
        exportInfo: {
          timestamp: new Date().toISOString(),
          totalRecords:
            usersSnapshot.size +
            predictionsSnapshot.size +
            symptomsSnapshot.size +
            hospitalsSnapshot.size,
        },
      };

      res.setHeader("Content-Type", "application/json");
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=hiv_predictor_full_export.json"
      );
      res.json(exportData);

      // Log admin action
      await db.collection("admin_logs").add({
        adminId: req.user.uid,
        action: "export_all_data",
        targetResource: "all_collections",
        metadata: { totalRecords: exportData.exportInfo.totalRecords },
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        result: "success",
        description: `Exported all data (${exportData.exportInfo.totalRecords} total records)`,
      });
    } catch (error) {
      console.error("Error exporting all data:", error);
      res.status(500).json({ error: "Failed to export all data" });
    }
  }
);

// Get system statistics (Admin only)
app.get("/admin/stats", authenticateUser, requireAdmin, async (req, res) => {
  try {
    const [usersSnapshot, predictionsSnapshot, highRiskSnapshot] =
      await Promise.all([
        db.collection("users").get(),
        db.collection("predictions").get(),
        db
          .collection("predictions")
          .where("riskLevel", "==", "High Risk")
          .get(),
      ]);

    const stats = {
      totalUsers: usersSnapshot.size,
      totalPredictions: predictionsSnapshot.size,
      highRiskPredictions: highRiskSnapshot.size,
      timestamp: new Date().toISOString(),
    };

    res.json(stats);
  } catch (error) {
    console.error("Error getting stats:", error);
    res.status(500).json({ error: "Failed to get statistics" });
  }
});

// Expose Express API as a single Cloud Function
export const api = functions.https.onRequest(app);
```

## 5. Database Collections Structure

### 📊 Complete Database Schema

```javascript
// Users Collection
{
  "id": "string (Firebase UID)",
  "username": "string (unique)",
  "email": "string",
  "fullName": "string",
  "createdAt": "timestamp",
  "lastLoginAt": "timestamp",
  "isAdmin": "boolean",
  "emailVerified": "boolean"
}

// Predictions Collection
{
  "id": "string (document ID)",
  "userId": "string (FK to users)",
  "prediction": "string",
  "confidence": "number (0.0-1.0)",
  "symptoms": {
    "fever": "number (0,1,2)",
    "fatigue": "number (0,1,2)",
    "weight_loss": "number (0,1,2)",
    // ... other symptoms
  },
  "riskLevel": "string (Low Risk|Medium Risk|High Risk)",
  "recommendations": ["array of strings"],
  "exposureLocations": "map (optional)",
  "locationWarning": "map (optional)",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}

// Notifications Collection
{
  "id": "string",
  "userId": "string (FK to users, null for global)",
  "type": "string (risk_alert|reminder|system|admin_alert)",
  "title": "string",
  "message": "string",
  "riskLevel": "string (optional)",
  "data": "map (additional data)",
  "isRead": "boolean",
  "isGlobal": "boolean",
  "priority": "string (low|medium|high|urgent)",
  "createdAt": "timestamp",
  "readAt": "timestamp (optional)",
  "expiresAt": "timestamp (optional)"
}

// Admin Logs Collection
{
  "id": "string",
  "adminId": "string (FK to users)",
  "action": "string",
  "targetUserId": "string (optional)",
  "targetResource": "string",
  "metadata": "map",
  "timestamp": "timestamp",
  "result": "string (success|failure)",
  "description": "string"
}

// Symptoms Collection (Reference Data)
{
  "name": "string (PK)",
  "displayName": "string",
  "description": "string",
  "category": "string",
  "severity": "number (1-10)",
  "isActive": "boolean",
  "options": ["array of response options"],
  "helpText": "string",
  "sortOrder": "number"
}

// Hospitals Collection (Reference Data)
{
  "id": "string",
  "name": "string",
  "address": "string",
  "latitude": "number",
  "longitude": "number",
  "phone": "string",
  "email": "string (optional)",
  "website": "string (optional)",
  "services": ["array of services"],
  "operatingHours": "map",
  "type": "string (public|private|clinic)",
  "isActive": "boolean",
  "rating": "number (optional)"
}
```

## 6. Deployment Instructions

### Step 1: Install Firebase CLI

```bash
npm install -g firebase-tools
```

### Step 2: Login to Firebase

```bash
firebase login
```

### Step 3: Initialize Firebase Project

```bash
firebase init
# Select: Firestore, Functions, Hosting, Storage
```

### Step 4: Configure Project

```bash
firebase use --add YOUR_PROJECT_ID
```

### Step 5: Deploy All Services

```bash
# Deploy everything
firebase deploy

# Or deploy specific services
firebase deploy --only firestore:rules
firebase deploy --only firestore:indexes
firebase deploy --only storage
firebase deploy --only functions
firebase deploy --only hosting
```

### Step 6: Test with Emulators

```bash
firebase emulators:start
```

## 7. Security Features

### 🔒 Authentication & Authorization

- ✅ Firebase Authentication integration
- ✅ Role-based access control (User/Admin)
- ✅ JWT token validation
- ✅ Rate limiting protection
- ✅ CORS security headers

### 🛡️ Data Protection

- ✅ Firestore security rules
- ✅ Storage security rules
- ✅ Input validation
- ✅ SQL injection prevention
- ✅ XSS protection

### 📊 Monitoring & Logging

- ✅ Admin action logging
- ✅ Error tracking
- ✅ Performance monitoring
- ✅ Security audit trails

## 8. API Endpoints

### Admin Data Export Endpoints

```
GET /admin/export/users?format=json|csv
GET /admin/export/predictions?format=json|csv
GET /admin/export/all
GET /admin/stats
```

### Authentication Required

- Bearer token in Authorization header
- Admin role verification
- Rate limiting applied

## 9. Cloud Functions Features

### 🔄 Automated Triggers

- **User Creation**: Auto-create user document
- **High Risk Detection**: Send notifications
- **Daily Cleanup**: Remove expired data
- **Weekly Reports**: Generate statistics

### 📤 Data Export

- **Multiple Formats**: JSON, CSV
- **Complete Export**: All collections
- **User Export**: User data only
- **Prediction Export**: Assessment data
- **Admin Logging**: Track all exports

### 📈 Analytics

- **System Statistics**: User counts, predictions
- **Risk Analysis**: High/medium/low risk trends
- **Performance Metrics**: Response times, errors

## 10. Production Checklist

### ✅ Before Going Live

- [ ] Update Firebase project configuration
- [ ] Set up custom domain
- [ ] Configure email/SMS notifications
- [ ] Set up monitoring alerts
- [ ] Test all security rules
- [ ] Backup strategy in place
- [ ] Performance optimization
- [ ] Load testing completed

### 🔧 Configuration Files

- `firebase/firestore.rules` - Database security
- `firebase/storage.rules` - File security
- `firebase/firebase.json` - Project configuration
- `firebase/firestore.indexes.json` - Query optimization
- `firebase/functions/` - Cloud Functions code

This complete Firebase setup provides enterprise-grade security, scalability, and functionality for your HIV Predictor App with full admin data export capabilities!
