class PredictionResult {
  final String? id; // Firebase document ID
  final String? userId; // Firebase user ID
  final String prediction;
  final double confidence;
  final Map<String, dynamic> symptoms; // Firebase compatible
  final String riskLevel;
  final List<String> recommendations;
  final Map<String, String>? exposureLocations;
  final Map<String, dynamic>? locationWarning;
  final DateTime createdAt;

  PredictionResult({
    this.id,
    this.userId,
    required this.prediction,
    required this.confidence,
    required this.symptoms,
    required this.riskLevel,
    required this.recommendations,
    this.exposureLocations,
    this.locationWarning,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'prediction': prediction,
      'confidence': confidence,
      'symptoms': symptoms,
      'riskLevel': riskLevel,
      'recommendations': recommendations,
      'exposureLocations': exposureLocations,
      'locationWarning': locationWarning,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory PredictionResult.fromMap(Map<String, dynamic> map) {
    return PredictionResult(
      id: map['id'],
      userId: map['userId'],
      prediction: map['prediction'] ?? '',
      confidence: (map['confidence'] ?? 0.0).toDouble(),
      symptoms: Map<String, dynamic>.from(map['symptoms'] ?? {}),
      riskLevel: map['riskLevel'] ?? 'Unknown',
      recommendations: List<String>.from(map['recommendations'] ?? []),
      exposureLocations: map['exposureLocations'] != null
          ? Map<String, String>.from(map['exposureLocations'])
          : null,
      locationWarning: map['locationWarning'] != null
          ? Map<String, dynamic>.from(map['locationWarning'])
          : null,
      createdAt: map['createdAt'] != null
          ? DateTime.parse(map['createdAt'])
          : DateTime.now(),
    );
  }

  // Firebase-compatible helper methods

  String get riskColor {
    switch (riskLevel) {
      case 'High':
        return 'bright_red'; // New bright red for high risk
      case 'Medium':
        return 'amber'; // New amber for medium risk
      case 'Low':
        return 'vibrant_green'; // New vibrant green for low risk
      default:
        return 'grey';
    }
  }
}
