import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:async';

class NewAdminDashboardScreen extends StatefulWidget {
  const NewAdminDashboardScreen({super.key});

  @override
  State<NewAdminDashboardScreen> createState() =>
      _NewAdminDashboardScreenState();
}

class _NewAdminDashboardScreenState extends State<NewAdminDashboardScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Dashboard statistics
  int _totalUsers = 0;
  int _totalPredictions = 0;
  int _highRiskCount = 0;
  int _mediumRiskCount = 0;
  int _lowRiskCount = 0;
  int _todayPredictions = 0;

  // Loading states
  bool _isInitialLoading = true; // Only show loading spinner on first load
  String _errorMessage = '';

  // Detailed data for display
  List<Map<String, dynamic>> _allUsers = [];
  List<Map<String, dynamic>> _recentPredictions = [];

  // Risk categorized users
  final List<Map<String, dynamic>> _highRiskUsers = [];
  final List<Map<String, dynamic>> _mediumRiskUsers = [];
  final List<Map<String, dynamic>> _lowRiskUsers = [];

  // Real-time update timer
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
    _startRealTimeUpdates();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  /// Start real-time updates every 30 seconds
  void _startRealTimeUpdates() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _loadDashboardData();
      }
    });
  }

  /// Load all dashboard statistics
  Future<void> _loadDashboardData() async {
    try {
      setState(() {
        // Only show loading spinner on initial load, not on refreshes
        _errorMessage = '';
      });

      // Load users with detailed information
      final usersSnapshot = await _firestore.collection('users').get();
      final totalUsers = usersSnapshot.docs.length;

      // Store detailed user information
      _allUsers = usersSnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'username': data['username'] ?? 'Unknown',
          'fullName': data['fullName'] ?? 'Unknown User',
          'email': data['email'] ?? 'No email',
          'isAdmin': data['isAdmin'] ?? false,
          'createdAt':
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
          'lastLoginAt': (data['lastLoginAt'] as Timestamp?)?.toDate(),
        };
      }).toList();

      // Load predictions with user information
      final predictionsSnapshot = await _firestore
          .collection('predictions')
          .get();
      final totalPredictions = predictionsSnapshot.docs.length;

      int highRisk = 0;
      int mediumRisk = 0;
      int lowRisk = 0;
      int todayPredictions = 0;

      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);

      // Store detailed prediction information with user data
      List<Map<String, dynamic>> allPredictions = [];

      for (var doc in predictionsSnapshot.docs) {
        final data = doc.data();
        final prediction = data['prediction']?.toString().toLowerCase() ?? '';
        final createdAt = (data['created_at'] as Timestamp?)?.toDate();
        final userId = data['user_id']?.toString() ?? '';

        // Find user information
        final user = _allUsers.firstWhere(
          (u) => u['id'] == userId,
          orElse: () => {'username': 'Unknown', 'fullName': 'Unknown User'},
        );

        // Store prediction with user info
        allPredictions.add({
          'id': doc.id,
          'prediction': data['prediction'] ?? 'Unknown',
          'riskLevel': data['risk_level'] ?? 'Unknown',
          'createdAt': createdAt ?? DateTime.now(),
          'userId': userId,
          'username': user['username'],
          'userFullName': user['fullName'],
          'symptoms': data['symptoms'] ?? [],
        });

        // Count risk levels (case-insensitive)
        final predictionLower = prediction.toLowerCase();
        if (predictionLower.contains('high')) {
          highRisk++;
        } else if (predictionLower.contains('medium')) {
          mediumRisk++;
        } else if (predictionLower.contains('low')) {
          lowRisk++;
        }

        // Count today's predictions
        if (createdAt != null && createdAt.isAfter(todayStart)) {
          todayPredictions++;
        }
      }

      // Sort predictions by date (newest first) and take recent ones
      allPredictions.sort((a, b) => b['createdAt'].compareTo(a['createdAt']));
      _recentPredictions = allPredictions.take(10).toList();

      // Categorize users by their latest risk level
      await _categorizeUsersByRisk();

      if (mounted) {
        setState(() {
          _totalUsers = totalUsers;
          _totalPredictions = totalPredictions;
          _highRiskCount = highRisk;
          _mediumRiskCount = mediumRisk;
          _lowRiskCount = lowRisk;
          _todayPredictions = todayPredictions;
          _isInitialLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error loading dashboard data: $e';
          _isInitialLoading = false;
        });
      }
    }
  }

  Future<void> _categorizeUsersByRisk() async {
    try {
      // Clear existing categorized lists
      _highRiskUsers.clear();
      _mediumRiskUsers.clear();
      _lowRiskUsers.clear();

      // Get all users and their latest predictions
      for (final user in _allUsers) {
        final userId = user['id'];

        // Get user's latest prediction
        final userPredictionsQuery = await _firestore
            .collection('predictions')
            .where('userId', isEqualTo: userId)
            .get();

        if (userPredictionsQuery.docs.isNotEmpty) {
          // Sort by creation date and get the latest prediction
          final userPredictions = userPredictionsQuery.docs.map((doc) {
            final data = doc.data();
            return {
              'prediction': data['prediction'] ?? '',
              'riskLevel': data['risk_level'] ?? '',
              'createdAt':
                  (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
            };
          }).toList();

          userPredictions.sort(
            (a, b) => b['createdAt'].compareTo(a['createdAt']),
          );
          final latestPrediction = userPredictions.first;

          final prediction = latestPrediction['prediction']
              .toString()
              .toLowerCase();
          final riskLevel = latestPrediction['riskLevel']
              .toString()
              .toLowerCase();

          // Create user data with risk info
          final userWithRisk = {
            ...user,
            'latestPrediction': latestPrediction['prediction'],
            'latestRiskLevel': latestPrediction['riskLevel'],
            'lastPredictionDate': latestPrediction['createdAt'],
          };

          // Categorize based on prediction content or risk level (case-insensitive)
          final predictionLower = prediction.toLowerCase();
          final riskLevelLower = riskLevel.toLowerCase();

          if (predictionLower.contains('high') ||
              riskLevelLower.contains('high')) {
            _highRiskUsers.add(userWithRisk);
          } else if (predictionLower.contains('medium') ||
              riskLevelLower.contains('medium')) {
            _mediumRiskUsers.add(userWithRisk);
          } else if (predictionLower.contains('low') ||
              riskLevelLower.contains('low')) {
            _lowRiskUsers.add(userWithRisk);
          }
        }
      }

      // Risk categorization completed successfully
    } catch (e) {
      // Error categorizing users by risk - handled silently
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Admin Dashboard',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: Colors.blue[700],
        elevation: 0,
        // Removed manual refresh button - using RefreshIndicator instead
      ),
      body: _isInitialLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading dashboard data...'),
                ],
              ),
            )
          : _errorMessage.isNotEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
                  const SizedBox(height: 16),
                  Text(
                    'Error Loading Data',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.red[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      _errorMessage,
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: _loadDashboardData,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            )
          : RefreshIndicator(
              onRefresh: _loadDashboardData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildWelcomeHeader(),
                    const SizedBox(height: 24),
                    _buildStatisticsGrid(),
                    const SizedBox(height: 24),
                    _buildQuickActions(),
                    const SizedBox(height: 24),
                    _buildRiskCategoriesSection(),
                    const SizedBox(height: 24),
                    _buildUsersSection(),
                    const SizedBox(height: 24),
                    _buildPredictionsSection(),
                    const SizedBox(height: 24),
                    _buildRecentActivity(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildWelcomeHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[700]!, Colors.blue[500]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Icon(Icons.admin_panel_settings, size: 48, color: Colors.white),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Welcome, Admin',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'HIV Predictor Management Dashboard',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.circle, size: 8, color: Colors.green[300]),
                    const SizedBox(width: 6),
                    Text(
                      'Real-time monitoring active',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Statistics Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'Total Users',
              _totalUsers.toString(),
              Icons.people,
              Colors.blue,
            ),
            _buildStatCard(
              'Total Predictions',
              _totalPredictions.toString(),
              Icons.analytics,
              Colors.green,
            ),
            _buildStatCard(
              'High Risk',
              _highRiskCount.toString(),
              Icons.warning,
              Colors.red,
            ),
            _buildStatCard(
              'Medium Risk',
              _mediumRiskCount.toString(),
              Icons.info,
              Colors.orange,
            ),
            _buildStatCard(
              'Low Risk',
              _lowRiskCount.toString(),
              Icons.check_circle,
              Colors.green,
            ),
            _buildStatCard(
              'Today\'s Predictions',
              _todayPredictions.toString(),
              Icons.today,
              Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 14, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'Manage Users',
                'View and manage all users',
                Icons.people_outline,
                Colors.blue,
                () => _navigateToUsers(),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionCard(
                'View Analytics',
                'Detailed prediction analytics',
                Icons.analytics_outlined,
                Colors.green,
                () => _navigateToAnalytics(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2)),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRiskCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Users by Risk Level',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildRiskCategoryCard(
                'High Risk',
                _highRiskUsers,
                Colors.red,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildRiskCategoryCard(
                'Medium Risk',
                _mediumRiskUsers,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildRiskCategoryCard(
                'Low Risk',
                _lowRiskUsers,
                Colors.green,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRiskCategoryCard(
    String title,
    List<Map<String, dynamic>> users,
    Color color,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  title == 'High Risk'
                      ? Icons.warning
                      : title == 'Medium Risk'
                      ? Icons.info
                      : Icons.check_circle,
                  color: color,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  '${users.length} users',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          if (users.isNotEmpty)
            Container(
              constraints: const BoxConstraints(maxHeight: 200),
              child: ListView.separated(
                shrinkWrap: true,
                padding: const EdgeInsets.all(12),
                itemCount: users.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final user = users[index];
                  return ListTile(
                    dense: true,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    leading: CircleAvatar(
                      radius: 16,
                      backgroundColor: color.withValues(alpha: 0.2),
                      child: Icon(
                        user['isAdmin']
                            ? Icons.admin_panel_settings
                            : Icons.person,
                        size: 16,
                        color: color,
                      ),
                    ),
                    title: Text(
                      user['fullName'] ?? 'Unknown User',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '@${user['username'] ?? 'unknown'}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        if (user['lastPredictionDate'] != null)
                          Text(
                            'Last: ${_formatDate(user['lastPredictionDate'])}',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[500],
                            ),
                          ),
                      ],
                    ),
                  );
                },
              ),
            )
          else
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'No users in this category',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildUsersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'All Users',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            Text(
              '${_allUsers.length} users',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _allUsers.length > 5 ? 5 : _allUsers.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final user = _allUsers[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: user['isAdmin']
                      ? Colors.red[100]
                      : Colors.blue[100],
                  child: Icon(
                    user['isAdmin'] ? Icons.admin_panel_settings : Icons.person,
                    color: user['isAdmin'] ? Colors.red[700] : Colors.blue[700],
                  ),
                ),
                title: Text(
                  user['fullName'],
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('@${user['username']}'),
                    Text(
                      user['email'],
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (user['isAdmin'])
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'ADMIN',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.red[700],
                          ),
                        ),
                      ),
                    const SizedBox(height: 4),
                    Text(
                      user['lastLoginAt'] != null
                          ? 'Last: ${_formatDate(user['lastLoginAt'])}'
                          : 'Never logged in',
                      style: TextStyle(fontSize: 10, color: Colors.grey[500]),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        if (_allUsers.length > 5)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Center(
              child: Text(
                'Showing 5 of ${_allUsers.length} users',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPredictionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Recent Predictions',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            Text(
              '${_recentPredictions.length} recent',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: _recentPredictions.isEmpty
              ? const Padding(
                  padding: EdgeInsets.all(32),
                  child: Center(
                    child: Text(
                      'No predictions yet',
                      style: TextStyle(color: Colors.grey, fontSize: 16),
                    ),
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _recentPredictions.length,
                  separatorBuilder: (context, index) =>
                      const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final prediction = _recentPredictions[index];
                    final riskLevel =
                        prediction['riskLevel']?.toString().toLowerCase() ?? '';
                    Color riskColor = Colors.grey;
                    IconData riskIcon = Icons.help;

                    if (riskLevel.contains('high')) {
                      riskColor = const Color(
                        0xFFFF3547,
                      ); // New bright red for high risk
                      riskIcon = Icons.warning;
                    } else if (riskLevel.contains('medium')) {
                      riskColor = const Color(
                        0xFFFFB300,
                      ); // New amber for medium risk
                      riskIcon = Icons.info;
                    } else if (riskLevel.contains('low')) {
                      riskColor = const Color(
                        0xFF00C851,
                      ); // New vibrant green for low risk
                      riskIcon = Icons.check_circle;
                    }

                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: riskColor.withValues(alpha: 0.1),
                        child: Icon(riskIcon, color: riskColor),
                      ),
                      title: Text(
                        prediction['userFullName'] ?? 'Unknown User',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('@${prediction['username'] ?? 'unknown'}'),
                          Text(
                            'Risk: ${prediction['riskLevel'] ?? 'Unknown'}',
                            style: TextStyle(
                              fontSize: 12,
                              color: riskColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            _formatDate(prediction['createdAt']),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: riskColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              riskLevel.toUpperCase(),
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: riskColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'System Activity',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildActivityItem(
                'High Risk Alert',
                'New high-risk prediction detected',
                Icons.warning,
                Colors.red,
                '2 min ago',
              ),
              const Divider(),
              _buildActivityItem(
                'New User Registration',
                'User joined the platform',
                Icons.person_add,
                Colors.blue,
                '15 min ago',
              ),
              const Divider(),
              _buildActivityItem(
                'Prediction Completed',
                'Medium risk assessment',
                Icons.analytics,
                Colors.orange,
                '1 hour ago',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    String time,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, size: 20, color: color),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  subtitle,
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
          Text(time, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        ],
      ),
    );
  }

  void _navigateToUsers() {
    // Show detailed user management dialog
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'User Management',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Total Users: ${_allUsers.length}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: _allUsers.length,
                  itemBuilder: (context, index) {
                    final user = _allUsers[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: user['isAdmin']
                              ? Colors.red[100]
                              : Colors.blue[100],
                          child: Icon(
                            user['isAdmin']
                                ? Icons.admin_panel_settings
                                : Icons.person,
                            color: user['isAdmin'] ? Colors.red : Colors.blue,
                          ),
                        ),
                        title: Text(
                          user['fullName'] ?? 'Unknown User',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('@${user['username'] ?? 'unknown'}'),
                            Text(
                              user['email'] ?? 'No email',
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                            if (user['lastLoginAt'] != null)
                              Text(
                                'Last login: ${_formatDate(user['lastLoginAt'])}',
                                style: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 12,
                                ),
                              ),
                          ],
                        ),
                        trailing: user['isAdmin']
                            ? Chip(
                                label: const Text('Admin'),
                                backgroundColor: Colors.red[100],
                                labelStyle: TextStyle(color: Colors.red[700]),
                              )
                            : null,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToAnalytics() {
    // Show detailed analytics dialog
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Prediction Analytics',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Overall Statistics
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Overall Statistics',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildAnalyticsCard(
                                      'Total Predictions',
                                      _totalPredictions.toString(),
                                      Icons.assessment,
                                      Colors.blue,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: _buildAnalyticsCard(
                                      'Today\'s Predictions',
                                      _todayPredictions.toString(),
                                      Icons.today,
                                      Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Risk Distribution
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Risk Level Distribution',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildAnalyticsCard(
                                      'High Risk',
                                      _highRiskCount.toString(),
                                      Icons.warning,
                                      Colors.red,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: _buildAnalyticsCard(
                                      'Medium Risk',
                                      _mediumRiskCount.toString(),
                                      Icons.info,
                                      Colors.orange,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: _buildAnalyticsCard(
                                      'Low Risk',
                                      _lowRiskCount.toString(),
                                      Icons.check_circle,
                                      Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // User Analytics
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'User Analytics',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildAnalyticsCard(
                                      'Total Users',
                                      _totalUsers.toString(),
                                      Icons.people,
                                      Colors.purple,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: _buildAnalyticsCard(
                                      'Avg Predictions/User',
                                      _totalUsers > 0
                                          ? (_totalPredictions / _totalUsers)
                                                .toStringAsFixed(1)
                                          : '0',
                                      Icons.analytics,
                                      Colors.teal,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnalyticsCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
