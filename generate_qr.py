#!/usr/bin/env python3
"""
Simple QR Code Generator for HIV Predictor App
"""

import qrcode
import socket
import webbrowser
import os

def get_local_ip():
    """Get the local IP address"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "*************"  # fallback IP

def generate_qr_code():
    """Generate QR code for app download"""
    
    # Get local IP
    local_ip = get_local_ip()
    
    # Create download URLs
    qr_page_url = f"http://{local_ip}:8080/qr_download_page.html"
    direct_apk_url = f"http://{local_ip}:8080/app-debug.apk"
    
    print("=" * 60)
    print("🏥 HIV PREDICTOR - QR CODE GENERATOR")
    print("=" * 60)
    print()
    print(f"📡 Your PC IP Address: {local_ip}")
    print(f"🌐 QR Page URL: {qr_page_url}")
    print(f"📱 Direct APK URL: {direct_apk_url}")
    print()
    
    # Generate QR code for the download page
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    
    qr.add_data(qr_page_url)
    qr.make(fit=True)
    
    # Create QR code image
    img = qr.make_image(fill_color="black", back_color="white")
    qr_filename = "hiv_predictor_qr.png"
    img.save(qr_filename)
    
    print(f"✅ QR Code generated: {qr_filename}")
    print()
    print("📱 SCAN INSTRUCTIONS:")
    print("   1. Open your phone's Camera app")
    print("   2. Point camera at the QR code image")
    print("   3. Tap the notification that appears")
    print("   4. Download and install the APK")
    print()
    print("🔧 REQUIREMENTS:")
    print("   • Phone and PC on same WiFi network")
    print("   • HTTP server running on port 8080")
    print("   • Unknown sources enabled on phone")
    print()
    
    # Try to open the QR code image
    try:
        if os.name == 'nt':  # Windows
            os.startfile(qr_filename)
        else:  # macOS/Linux
            webbrowser.open(qr_filename)
        print(f"🖼️  QR code image opened: {qr_filename}")
    except:
        print(f"📁 QR code saved as: {qr_filename}")
        print("   Please open this file to see the QR code")
    
    print()
    print("=" * 60)
    
    return qr_filename, qr_page_url, direct_apk_url

if __name__ == "__main__":
    generate_qr_code()
    input("\nPress Enter to exit...")
