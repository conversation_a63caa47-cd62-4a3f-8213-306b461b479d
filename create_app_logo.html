but <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HIV Predictor - Logo Design</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5em;
        }
        
        .logo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .logo-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .logo-card:hover {
            transform: translateY(-5px);
        }
        
        .logo-container {
            width: 200px;
            height: 200px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .logo-svg {
            width: 150px;
            height: 150px;
        }
        
        .logo-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .logo-desc {
            opacity: 0.8;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .download-btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
        }
        
        .download-btn:hover {
            background: #45a049;
        }
        
        .sizes {
            margin-top: 20px;
            font-size: 0.8em;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 HIV Predictor - Logo Options</h1>
        
        <div class="logo-grid">
            <!-- Logo Option 1: Medical Cross with Shield -->
            <div class="logo-card">
                <div class="logo-container">
                    <svg class="logo-svg" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <!-- Shield background -->
                        <path d="M100 20 L160 40 L160 100 Q160 140 100 180 Q40 140 40 100 L40 40 Z" 
                              fill="#2196F3" stroke="#1976D2" stroke-width="2"/>
                        
                        <!-- Medical cross -->
                        <rect x="90" y="60" width="20" height="80" fill="white" rx="3"/>
                        <rect x="70" y="90" width="60" height="20" fill="white" rx="3"/>
                        
                        <!-- DNA helix -->
                        <path d="M60 50 Q80 60 100 50 Q120 40 140 50" 
                              stroke="#FF5722" stroke-width="3" fill="none"/>
                        <path d="M60 150 Q80 140 100 150 Q120 160 140 150" 
                              stroke="#FF5722" stroke-width="3" fill="none"/>
                        
                        <!-- Small dots for data/AI -->
                        <circle cx="75" cy="75" r="3" fill="#4CAF50"/>
                        <circle cx="125" cy="75" r="3" fill="#4CAF50"/>
                        <circle cx="75" cy="125" r="3" fill="#4CAF50"/>
                        <circle cx="125" cy="125" r="3" fill="#4CAF50"/>
                    </svg>
                </div>
                <div class="logo-title">Medical Shield</div>
                <div class="logo-desc">Professional medical design with shield protection, cross symbol, and AI data points</div>
                <div class="sizes">Available: 512x512, 256x256, 128x128, 64x64</div>
            </div>
            
            <!-- Logo Option 2: Heart with Pulse -->
            <div class="logo-card">
                <div class="logo-container">
                    <svg class="logo-svg" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <!-- Heart shape -->
                        <path d="M100 160 C80 140, 40 120, 40 80 C40 60, 60 40, 80 40 C90 40, 100 50, 100 50 C100 50, 110 40, 120 40 C140 40, 160 60, 160 80 C160 120, 120 140, 100 160 Z" 
                              fill="#E91E63" stroke="#C2185B" stroke-width="2"/>
                        
                        <!-- Pulse line -->
                        <path d="M30 100 L60 100 L70 80 L80 120 L90 60 L100 140 L110 80 L120 100 L170 100" 
                              stroke="white" stroke-width="4" fill="none"/>
                        
                        <!-- Medical cross in heart -->
                        <rect x="95" y="75" width="10" height="40" fill="white" rx="2"/>
                        <rect x="85" y="90" width="30" height="10" fill="white" rx="2"/>
                        
                        <!-- AI circuit pattern -->
                        <circle cx="100" cy="50" r="4" fill="#4CAF50"/>
                        <line x1="100" y1="50" x2="100" y2="70" stroke="#4CAF50" stroke-width="2"/>
                    </svg>
                </div>
                <div class="logo-title">Health Pulse</div>
                <div class="logo-desc">Heart-centered design with health monitoring pulse and medical cross integration</div>
                <div class="sizes">Available: 512x512, 256x256, 128x128, 64x64</div>
            </div>
            
            <!-- Logo Option 3: Modern Hexagon -->
            <div class="logo-card">
                <div class="logo-container">
                    <svg class="logo-svg" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <!-- Hexagon background -->
                        <polygon points="100,20 160,60 160,140 100,180 40,140 40,60" 
                                fill="#673AB7" stroke="#512DA8" stroke-width="3"/>
                        
                        <!-- Inner hexagon -->
                        <polygon points="100,40 140,70 140,130 100,160 60,130 60,70" 
                                fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="2"/>
                        
                        <!-- Medical symbol -->
                        <circle cx="100" cy="100" r="25" fill="white"/>
                        <rect x="95" y="85" width="10" height="30" fill="#673AB7" rx="2"/>
                        <rect x="85" y="95" width="30" height="10" fill="#673AB7" rx="2"/>
                        
                        <!-- Data nodes -->
                        <circle cx="80" cy="80" r="4" fill="#FF9800"/>
                        <circle cx="120" cy="80" r="4" fill="#FF9800"/>
                        <circle cx="80" cy="120" r="4" fill="#FF9800"/>
                        <circle cx="120" cy="120" r="4" fill="#FF9800"/>
                        
                        <!-- Connection lines -->
                        <line x1="80" y1="80" x2="100" y2="100" stroke="#FF9800" stroke-width="2"/>
                        <line x1="120" y1="80" x2="100" y2="100" stroke="#FF9800" stroke-width="2"/>
                        <line x1="80" y1="120" x2="100" y2="100" stroke="#FF9800" stroke-width="2"/>
                        <line x1="120" y1="120" x2="100" y2="100" stroke="#FF9800" stroke-width="2"/>
                    </svg>
                </div>
                <div class="logo-title">Tech Medical</div>
                <div class="logo-desc">Modern hexagonal design representing AI technology with medical cross and data connections</div>
                <div class="sizes">Available: 512x512, 256x256, 128x128, 64x64</div>
            </div>
            
            <!-- Logo Option 4: Circular Badge -->
            <div class="logo-card">
                <div class="logo-container">
                    <svg class="logo-svg" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <!-- Outer circle -->
                        <circle cx="100" cy="100" r="80" fill="#009688" stroke="#00796B" stroke-width="4"/>
                        
                        <!-- Inner circle -->
                        <circle cx="100" cy="100" r="60" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="2"/>
                        
                        <!-- Medical cross -->
                        <rect x="90" y="70" width="20" height="60" fill="white" rx="3"/>
                        <rect x="70" y="90" width="60" height="20" fill="white" rx="3"/>
                        
                        <!-- HIV ribbon -->
                        <path d="M70 50 Q100 30 130 50 Q130 70 100 70 Q70 70 70 50" 
                              fill="#DC143C" stroke="#B71C1C" stroke-width="2"/>
                        
                        <!-- Stethoscope curve -->
                        <path d="M60 120 Q80 140 100 140 Q120 140 140 120" 
                              stroke="white" stroke-width="4" fill="none"/>
                        <circle cx="60" cy="120" r="6" fill="white"/>
                        <circle cx="140" cy="120" r="6" fill="white"/>
                        
                        <!-- AI dots -->
                        <circle cx="85" cy="85" r="2" fill="#FFC107"/>
                        <circle cx="115" cy="85" r="2" fill="#FFC107"/>
                        <circle cx="85" cy="115" r="2" fill="#FFC107"/>
                        <circle cx="115" cy="115" r="2" fill="#FFC107"/>
                    </svg>
                </div>
                <div class="logo-title">Medical Badge</div>
                <div class="logo-desc">Professional circular badge with medical cross, HIV awareness ribbon, and stethoscope</div>
                <div class="sizes">Available: 512x512, 256x256, 128x128, 64x64</div>
            </div>
            
            <!-- Logo Option 5: Minimalist -->
            <div class="logo-card">
                <div class="logo-container">
                    <svg class="logo-svg" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background circle -->
                        <circle cx="100" cy="100" r="75" fill="#4CAF50" stroke="#388E3C" stroke-width="3"/>
                        
                        <!-- Medical cross - modern style -->
                        <rect x="85" y="65" width="30" height="70" fill="white" rx="8"/>
                        <rect x="65" y="85" width="70" height="30" fill="white" rx="8"/>
                        
                        <!-- Overlap circle for modern look -->
                        <circle cx="100" cy="100" r="20" fill="#4CAF50"/>
                        
                        <!-- Small medical symbol -->
                        <rect x="95" y="90" width="10" height="20" fill="white" rx="2"/>
                        <rect x="90" y="95" width="20" height="10" fill="white" rx="2"/>
                        
                        <!-- Corner accents -->
                        <circle cx="130" cy="70" r="4" fill="#FF5722"/>
                        <circle cx="70" cy="130" r="4" fill="#FF5722"/>
                        <circle cx="130" cy="130" r="4" fill="#2196F3"/>
                        <circle cx="70" cy="70" r="4" fill="#2196F3"/>
                    </svg>
                </div>
                <div class="logo-title">Clean Medical</div>
                <div class="logo-desc">Clean, minimalist design with modern medical cross and subtle accent colors</div>
                <div class="sizes">Available: 512x512, 256x256, 128x128, 64x64</div>
            </div>
            
            <!-- Logo Option 6: App Icon Style -->
            <div class="logo-card">
                <div class="logo-container">
                    <svg class="logo-svg" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <!-- Rounded square background -->
                        <rect x="20" y="20" width="160" height="160" fill="#1976D2" rx="35" stroke="#1565C0" stroke-width="3"/>
                        
                        <!-- Inner gradient effect -->
                        <defs>
                            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect x="30" y="30" width="140" height="140" fill="url(#grad1)" rx="25"/>
                        
                        <!-- Large medical cross -->
                        <rect x="85" y="60" width="30" height="80" fill="white" rx="6"/>
                        <rect x="60" y="85" width="80" height="30" fill="white" rx="6"/>
                        
                        <!-- HIV awareness ribbon -->
                        <path d="M60 45 Q100 25 140 45 Q140 55 100 55 Q60 55 60 45" 
                              fill="#DC143C"/>
                        
                        <!-- Small plus signs for medical theme -->
                        <text x="50" y="160" fill="white" font-size="20" font-weight="bold">+</text>
                        <text x="130" y="160" fill="white" font-size="20" font-weight="bold">+</text>
                        
                        <!-- AI indicator -->
                        <circle cx="150" cy="50" r="8" fill="#4CAF50"/>
                        <text x="146" y="55" fill="white" font-size="10" font-weight="bold">AI</text>
                    </svg>
                </div>
                <div class="logo-title">App Icon</div>
                <div class="logo-desc">Perfect for mobile app icon with rounded corners, medical cross, and AI indicator</div>
                <div class="sizes">Available: 512x512, 256x256, 128x128, 64x64</div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 40px 0; padding: 30px; background: rgba(255,255,255,0.1); border-radius: 20px;">
            <h2>🎨 Logo Customization Options</h2>
            <p>All logos can be customized with:</p>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li><strong>Colors:</strong> Match your brand colors or medical theme</li>
                <li><strong>Text:</strong> Add "HIV Predictor" text below or around the logo</li>
                <li><strong>Sizes:</strong> Multiple resolutions for different uses</li>
                <li><strong>Formats:</strong> PNG, SVG, ICO for different platforms</li>
                <li><strong>Variations:</strong> Light/dark versions, monochrome options</li>
            </ul>
            
            <div style="margin-top: 30px;">
                <h3>📱 Usage Recommendations:</h3>
                <p><strong>App Icon:</strong> Option 6 (App Icon Style)<br>
                <strong>Website Header:</strong> Option 1 (Medical Shield)<br>
                <strong>Business Cards:</strong> Option 4 (Medical Badge)<br>
                <strong>Social Media:</strong> Option 2 (Health Pulse)</p>
            </div>
        </div>
    </div>
    
    <script>
        // Add click handlers to download logos
        document.querySelectorAll('.logo-card').forEach((card, index) => {
            card.addEventListener('click', () => {
                const svg = card.querySelector('.logo-svg');
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const data = new XMLSerializer().serializeToString(svg);
                const DOMURL = window.URL || window.webkitURL || window;
                
                const img = new Image();
                const svgBlob = new Blob([data], {type: 'image/svg+xml;charset=utf-8'});
                const url = DOMURL.createObjectURL(svgBlob);
                
                img.onload = function () {
                    canvas.width = 512;
                    canvas.height = 512;
                    ctx.drawImage(img, 0, 0, 512, 512);
                    DOMURL.revokeObjectURL(url);
                    
                    const imgURI = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream');
                    const evt = new MouseEvent('click', {
                        view: window,
                        bubbles: false,
                        cancelable: true
                    });
                    
                    const a = document.createElement('a');
                    a.setAttribute('download', `hiv-predictor-logo-${index + 1}.png`);
                    a.setAttribute('href', imgURI);
                    a.setAttribute('target', '_blank');
                    a.dispatchEvent(evt);
                };
                
                img.src = url;
            });
        });
    </script>
</body>
</html>
