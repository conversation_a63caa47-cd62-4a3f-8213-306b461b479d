@echo off
title HIV Predictor - Firebase Hosting Deployment
color 0A

echo.
echo ========================================
echo   🔥 Firebase Hosting Deployment
echo ========================================
echo.

echo 🔧 Prerequisites Check...
echo.

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Firebase CLI not installed
    echo.
    echo 📥 Installing Firebase CLI...
    npm install -g firebase-tools
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Failed to install Firebase CLI
        echo 💡 Please install Node.js first: https://nodejs.org/
        pause
        exit /b 1
    )
)

echo ✅ Firebase CLI ready
echo.

echo 🏗️ Building web app for production...
flutter build web --release

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Web build failed
    pause
    exit /b 1
)

echo ✅ Web build completed
echo.

echo 🔥 Firebase Setup...
echo.

REM Check if firebase.json exists
if not exist "firebase.json" (
    echo 📝 Initializing Firebase project...
    echo.
    echo 🔑 Please login to Firebase when prompted
    firebase login
    
    echo.
    echo 🎯 Initializing hosting...
    firebase init hosting
) else (
    echo ✅ Firebase already configured
)

echo.
echo 🚀 Deploying to Firebase Hosting...
echo.

firebase deploy --only hosting

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   ✅ DEPLOYMENT SUCCESSFUL!
    echo ========================================
    echo.
    echo 🌐 Your HIV Predictor app is now live!
    echo.
    echo 📱 Access URLs:
    echo    • Firebase URL: https://hiv-predictor-app.web.app
    echo    • Custom Domain: https://hiv-predictor-app.firebaseapp.com
    echo.
    echo 💰 Cost: $0.00 (FREE tier)
    echo 📊 Usage: Monitor at https://console.firebase.google.com
    echo.
    echo 🎯 Features Available:
    echo    • Global CDN
    echo    • Automatic SSL
    echo    • Custom domains
    echo    • Analytics
    echo    • Version history
    echo.
) else (
    echo ❌ Deployment failed
    echo 💡 Check your internet connection and Firebase permissions
)

echo.
pause
