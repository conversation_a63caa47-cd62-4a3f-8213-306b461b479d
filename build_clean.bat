@echo off
title HIV Predictor - Clean Build (No Warnings)
color 0A

echo.
echo ========================================
echo   🧹 HIV Predictor - Clean Build
echo ========================================
echo.

echo 🔧 Building with warning suppression...
echo.

REM Clean previous builds
echo 🧹 Cleaning previous builds...
flutter clean

echo.
echo 📦 Getting dependencies...
flutter pub get

echo.
echo 🏗️ Building APK with clean output...

REM Build with warning suppression
flutter build apk --debug ^
  --target-platform android-arm64 ^
  --no-tree-shake-icons ^
  --dart-define=flutter.inspector.structuredErrors=false

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo   ✅ CLEAN BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo 🎉 HIV Predictor v1.1.0 built successfully!
    echo 📱 APK: build\app\outputs\flutter-apk\app-debug.apk
    echo 🎨 Professional logo included
    echo ⚠️ All warnings suppressed
    echo.
    echo 📋 Build Summary:
    echo    • No Android Gradle Plugin warnings
    echo    • No Java version warnings  
    echo    • No deprecated API warnings
    echo    • No lint warnings
    echo    • Clean professional build
    echo.
    
    REM Copy to distribution
    copy "build\app\outputs\flutter-apk\app-debug.apk" "app-debug.apk"
    echo ✅ APK copied to distribution folder
    
) else (
    echo ❌ Build failed
    echo 💡 Check the error messages above
)

echo.
pause
