{"name": "hiv-predictor-functions", "version": "1.0.0", "description": "Cloud Functions for HIV Predictor App", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^11.8.0", "firebase-functions": "^4.3.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "rate-limiter-flexible": "^2.4.1", "node-cron": "^3.0.2", "csv-writer": "^1.6.0", "json2csv": "^6.1.0", "nodemailer": "^6.9.3", "twilio": "^4.11.1"}, "devDependencies": {"typescript": "^4.9.0", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0"}, "private": true}