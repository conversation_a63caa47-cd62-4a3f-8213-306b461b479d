@echo off
title HIV Predictor - App Upgrade System
color 0A

echo.
echo ========================================
echo   🔄 HIV Predictor - App Upgrade System
echo ========================================
echo.

set /p NEW_VERSION="Enter new version (e.g., 1.0.1): "
set /p BUILD_NUMBER="Enter new build number (e.g., 2): "
set /p RELEASE_NOTES="Enter release notes: "

echo.
echo 🔧 Updating version information...

REM Update pubspec.yaml version
echo Updating pubspec.yaml...
powershell -Command "(Get-Content pubspec.yaml) -replace 'version: .*', 'version: %NEW_VERSION%+%BUILD_NUMBER%' | Set-Content pubspec.yaml"

echo.
echo 🏗️ Building new version...

REM Clean and build
flutter clean
flutter pub get
flutter build apk --debug

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo.
echo 📦 Copying new APK...
copy "build\app\outputs\flutter-apk\app-debug.apk" "app-debug.apk"

echo.
echo 🌐 Building web version...
flutter build web --debug
if %ERRORLEVEL% EQU 0 (
    echo Copying web build...
    if exist "web" rmdir /s /q "web"
    xcopy "build\web" "web\" /e /i /q
)

echo.
echo 📝 Updating version.json...

REM Create updated version.json
powershell -Command "
$json = Get-Content 'version.json' | ConvertFrom-Json
$json.version = '%NEW_VERSION%'
$json.buildNumber = [int]'%BUILD_NUMBER%'
$json.releaseNotes = '%RELEASE_NOTES%'
$json.serverInfo.lastUpdated = (Get-Date).ToString('yyyy-MM-ddTHH:mm:ssZ')

# Add to changelog
$newChange = @{
    version = '%NEW_VERSION%'
    buildNumber = [int]'%BUILD_NUMBER%'
    date = (Get-Date).ToString('yyyy-MM-dd')
    changes = @('%RELEASE_NOTES%')
}
$json.changelog = @($newChange) + $json.changelog

$json | ConvertTo-Json -Depth 10 | Set-Content 'version.json'
"

echo.
echo ========================================
echo   ✅ Upgrade Complete!
echo ========================================
echo.
echo 📱 New Version: %NEW_VERSION% (Build %BUILD_NUMBER%)
echo 📦 APK: app-debug.apk
echo 🌐 Web: web/
echo 📝 Version info: version.json
echo.
echo 🚀 Distribution URLs:
echo    • Android: http://172.30.18.51:8080/app-debug.apk
echo    • Web App: http://172.30.18.51:8080/web/
echo    • Download Page: http://172.30.18.51:8080/multi_platform_download.html
echo.
echo 📢 Users will be notified of the update when they open the app!
echo.
pause
