import 'package:flutter/material.dart';
import '../models/prediction_result.dart';
import '../models/user.dart';

class DashboardKPIWidget extends StatelessWidget {
  final User user;
  final List<PredictionResult> predictions;

  const DashboardKPIWidget({
    super.key,
    required this.user,
    required this.predictions,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.dashboard,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Health KPIs',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.trending_up, size: 12, color: Color(0xFF10B981)),
                    SizedBox(width: 4),
                    Text(
                      'Live',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF10B981),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // KPI Grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              _buildKPICard(
                'Health Score',
                '${_calculateHealthScore().round()}',
                '/100',
                Icons.favorite,
                _getHealthScoreColor(),
                _getHealthScoreTrend(),
              ),
              _buildKPICard(
                'Risk Level',
                _getCurrentRiskLevel(),
                '',
                Icons.security,
                _getRiskLevelColor(),
                _getRiskTrend(),
              ),
              _buildKPICard(
                'Streak',
                '${_calculateStreak()}',
                'days',
                Icons.local_fire_department,
                const Color(0xFFFF6B35),
                _getStreakTrend(),
              ),
              _buildKPICard(
                'Assessments',
                '${predictions.length}',
                'total',
                Icons.assessment,
                const Color(0xFF6366F1),
                _getAssessmentTrend(),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Progress Indicators
          _buildProgressSection(),
        ],
      ),
    );
  }

  Widget _buildKPICard(
    String title,
    String value,
    String unit,
    IconData icon,
    Color color,
    String trend,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, color: Colors.white, size: 14),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: trend.startsWith('+')
                      ? const Color(0xFF10B981).withValues(alpha: 0.1)
                      : trend.startsWith('-')
                      ? const Color(0xFFEF4444).withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  trend,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: trend.startsWith('+')
                        ? const Color(0xFF10B981)
                        : trend.startsWith('-')
                        ? const Color(0xFFEF4444)
                        : Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              if (unit.isNotEmpty) ...[
                const SizedBox(width: 2),
                Text(
                  unit,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    const healthGoal = 85.0;
    final currentScore = _calculateHealthScore();
    final progress = (currentScore / healthGoal).clamp(0.0, 1.0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Health Goal Progress',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2C3E50),
              ),
            ),
            const Spacer(),
            Text(
              '${(progress * 100).round()}%',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Color(0xFF6366F1),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(4),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress,
            child: Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                ),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Target: ${healthGoal.round()} points',
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  double _calculateHealthScore() {
    if (predictions.isEmpty) return 50.0;

    double totalScore = 0;
    for (final prediction in predictions) {
      final risk = prediction.riskLevel.toLowerCase();
      if (risk.contains('low')) {
        totalScore += 85;
      } else if (risk.contains('medium')) {
        totalScore += 60;
      } else {
        totalScore += 30;
      }
    }

    final averageScore = totalScore / predictions.length;
    final streak = _calculateStreak();
    final consistencyBonus = (streak * 0.5).clamp(0, 15);

    return (averageScore + consistencyBonus).clamp(0, 100);
  }

  String _getCurrentRiskLevel() {
    if (predictions.isEmpty) return 'Unknown';
    final risk = predictions.first.riskLevel.toLowerCase();
    if (risk.contains('low')) return 'Low';
    if (risk.contains('medium')) return 'Medium';
    return 'High';
  }

  int _calculateStreak() {
    if (predictions.isEmpty) return 0;

    final now = DateTime.now();
    int streak = 0;

    for (final prediction in predictions) {
      final daysDiff = now.difference(prediction.createdAt).inDays;
      if (daysDiff <= streak + 1) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  Color _getHealthScoreColor() {
    final score = _calculateHealthScore();
    if (score >= 80) {
      return const Color(0xFF00C851); // New vibrant green for low risk
    }
    if (score >= 60) {
      return const Color(0xFFFFB300); // New amber for medium risk
    }
    return const Color(0xFFFF3547); // New bright red for high risk
  }

  Color _getRiskLevelColor() {
    final risk = _getCurrentRiskLevel().toLowerCase();
    if (risk == 'low') {
      return const Color(0xFF00C851); // New vibrant green for low risk
    }
    if (risk == 'medium') {
      return const Color(0xFFFFB300); // New amber for medium risk
    }
    return const Color(0xFFFF3547); // New bright red for high risk
  }

  String _getHealthScoreTrend() {
    if (predictions.length < 2) return 'New';

    final currentScore = _calculateHealthScore();
    final previousPredictions = predictions.skip(1).toList();
    final previousScore = _calculatePreviousHealthScore(previousPredictions);

    final difference = currentScore - previousScore;
    if (difference > 2) return '+${difference.round()}';
    if (difference < -2) return '${difference.round()}';
    return '0';
  }

  String _getRiskTrend() {
    if (predictions.length < 2) return 'New';

    final current = predictions.first.riskLevel.toLowerCase();
    final previous = predictions[1].riskLevel.toLowerCase();

    final currentScore = _getRiskScore(current);
    final previousScore = _getRiskScore(previous);

    if (currentScore > previousScore) return '+1';
    if (currentScore < previousScore) return '-1';
    return '0';
  }

  String _getStreakTrend() {
    final streak = _calculateStreak();
    if (streak == 0) return 'Start';
    if (streak >= 7) return 'Hot';
    if (streak >= 3) return 'Good';
    return '+$streak';
  }

  String _getAssessmentTrend() {
    final count = predictions.length;
    if (count == 0) return 'Start';
    if (count >= 10) return 'Expert';
    if (count >= 5) return 'Active';
    return '+$count';
  }

  double _calculatePreviousHealthScore(
    List<PredictionResult> previousPredictions,
  ) {
    if (previousPredictions.isEmpty) return 50.0;

    double totalScore = 0;
    for (final prediction in previousPredictions) {
      final risk = prediction.riskLevel.toLowerCase();
      if (risk.contains('low')) {
        totalScore += 85;
      } else if (risk.contains('medium')) {
        totalScore += 60;
      } else {
        totalScore += 30;
      }
    }

    return totalScore / previousPredictions.length;
  }

  int _getRiskScore(String riskLevel) {
    if (riskLevel.contains('low')) return 3;
    if (riskLevel.contains('medium')) return 2;
    return 1;
  }
}
