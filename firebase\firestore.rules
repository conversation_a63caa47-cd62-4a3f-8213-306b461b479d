rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    function isValidUser(userData) {
      return userData.keys().hasAll(['username', 'email', 'fullName', 'createdAt', 'isAdmin']) &&
             userData.username is string &&
             userData.email is string &&
             userData.fullName is string &&
             userData.createdAt is timestamp &&
             userData.isAdmin is bool;
    }
    
    function isValidPrediction(predictionData) {
      return predictionData.keys().hasAll(['userId', 'prediction', 'confidence', 'symptoms', 'riskLevel', 'recommendations', 'createdAt']) &&
             predictionData.userId is string &&
             predictionData.prediction is string &&
             predictionData.confidence is number &&
             predictionData.symptoms is map &&
             predictionData.riskLevel is string &&
             predictionData.recommendations is list &&
             predictionData.createdAt is timestamp;
    }
    
    // Users collection
    match /users/{userId} {
      // Users can read and write their own data
      allow read, write: if isAuthenticated() && isOwner(userId);
      
      // Admins can read all users
      allow read: if isAdmin();
      
      // Allow user creation during signup
      allow create: if isAuthenticated() && 
                       isOwner(userId) && 
                       isValidUser(resource.data);
      
      // Allow user updates (profile changes)
      allow update: if isAuthenticated() && 
                       isOwner(userId) && 
                       isValidUser(resource.data);
      
      // Only admins can delete users (or users can delete themselves)
      allow delete: if isAuthenticated() && (isOwner(userId) || isAdmin());
    }
    
    // Predictions collection
    match /predictions/{predictionId} {
      // Users can read their own predictions
      allow read: if isAuthenticated() && 
                     (isOwner(resource.data.userId) || isAdmin());
      
      // Users can create their own predictions
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.userId) && 
                       isValidPrediction(request.resource.data);
      
      // Users can update their own predictions
      allow update: if isAuthenticated() && 
                       isOwner(resource.data.userId) && 
                       isValidPrediction(request.resource.data);
      
      // Users can delete their own predictions, admins can delete any
      allow delete: if isAuthenticated() && 
                       (isOwner(resource.data.userId) || isAdmin());
    }
    
    // Symptoms collection (reference data)
    match /symptoms/{symptomId} {
      // Anyone authenticated can read symptoms
      allow read: if isAuthenticated();
      
      // Only admins can modify symptoms
      allow write: if isAdmin();
    }
    
    // Hospitals collection (reference data)
    match /hospitals/{hospitalId} {
      // Anyone authenticated can read hospitals
      allow read: if isAuthenticated();
      
      // Only admins can modify hospitals
      allow write: if isAdmin();
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      // Users can read their own notifications or global notifications
      allow read: if isAuthenticated() && 
                     (isOwner(resource.data.userId) || 
                      resource.data.isGlobal == true || 
                      isAdmin());
      
      // System can create notifications, users can update read status
      allow create: if isAuthenticated();
      
      // Users can update their own notifications (mark as read)
      allow update: if isAuthenticated() && 
                       (isOwner(resource.data.userId) || isAdmin());
      
      // Only admins can delete notifications
      allow delete: if isAdmin();
    }
    
    // Admin logs collection
    match /admin_logs/{logId} {
      // Only admins can read admin logs
      allow read: if isAdmin();
      
      // Only admins can create admin logs
      allow create: if isAdmin();
      
      // Admin logs are immutable (no updates or deletes)
      allow update, delete: if false;
    }
    
    // Education content collection
    match /education_content/{contentId} {
      // Anyone authenticated can read published content
      allow read: if isAuthenticated() && 
                     (resource.data.isPublished == true || isAdmin());
      
      // Only admins can modify education content
      allow write: if isAdmin();
    }
    
    // System settings collection
    match /system_settings/{settingId} {
      // Anyone can read public settings, admins can read all
      allow read: if isAuthenticated() && 
                     (resource.data.isPublic == true || isAdmin());
      
      // Only admins can modify system settings
      allow write: if isAdmin();
    }
    
    // Analytics collection
    match /analytics/{eventId} {
      // Users can create their own analytics events
      allow create: if isAuthenticated() && 
                       (isOwner(request.resource.data.userId) || 
                        request.resource.data.userId == null);
      
      // Only admins can read analytics
      allow read: if isAdmin();
      
      // Analytics are immutable (no updates or deletes)
      allow update, delete: if false;
    }
    
    // Feedback collection
    match /feedback/{feedbackId} {
      // Users can read their own feedback, admins can read all
      allow read: if isAuthenticated() && 
                     (isOwner(resource.data.userId) || isAdmin());
      
      // Users can create feedback
      allow create: if isAuthenticated();
      
      // Users can update their own feedback, admins can update any
      allow update: if isAuthenticated() && 
                       (isOwner(resource.data.userId) || isAdmin());
      
      // Only admins can delete feedback
      allow delete: if isAdmin();
    }
    
    // Default deny rule
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
