#!/usr/bin/env python3
"""
HIV Predictor App - Professional Icon Creator
Creates the App Icon Style (Option 6) in all required sizes
"""

from PIL import Image, ImageDraw
import os

def create_hiv_predictor_icon(size):
    """Create the HIV Predictor app icon (Option 6 - App Icon Style)"""
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Calculate proportional sizes
    margin = size // 10
    corner_radius = size // 6
    
    # Background rounded rectangle (blue gradient effect)
    bg_color = (25, 118, 210, 255)  # #1976D2
    inner_color = (33, 150, 243, 255)  # #2196F3
    
    # Outer background
    draw.rounded_rectangle(
        [margin, margin, size - margin, size - margin],
        radius=corner_radius,
        fill=bg_color
    )
    
    # Inner gradient effect
    inner_margin = margin + size // 20
    inner_radius = corner_radius - size // 40
    
    draw.rounded_rectangle(
        [inner_margin, inner_margin, size - inner_margin, size - inner_margin],
        radius=inner_radius,
        fill=inner_color
    )
    
    # Medical cross (white) - large and prominent
    cross_color = (255, 255, 255, 255)
    cross_width = size // 6
    cross_length = size // 2.2
    cross_radius = size // 25
    center = size // 2
    
    # Vertical part of cross
    v_left = center - cross_width // 2
    v_right = center + cross_width // 2
    v_top = center - cross_length // 2
    v_bottom = center + cross_length // 2
    
    draw.rounded_rectangle(
        [v_left, v_top, v_right, v_bottom],
        radius=cross_radius,
        fill=cross_color
    )
    
    # Horizontal part of cross
    h_left = center - cross_length // 2
    h_right = center + cross_length // 2
    h_top = center - cross_width // 2
    h_bottom = center + cross_width // 2
    
    draw.rounded_rectangle(
        [h_left, h_top, h_right, h_bottom],
        radius=cross_radius,
        fill=cross_color
    )
    
    # HIV awareness ribbon (red) - at top
    ribbon_color = (220, 20, 60, 255)  # #DC143C
    ribbon_y = margin + size // 8
    ribbon_height = size // 15
    ribbon_width = size // 2.5
    
    # Ribbon shape
    ribbon_left = center - ribbon_width // 2
    ribbon_right = center + ribbon_width // 2
    
    draw.rounded_rectangle(
        [ribbon_left, ribbon_y, ribbon_right, ribbon_y + ribbon_height],
        radius=ribbon_height // 3,
        fill=ribbon_color
    )
    
    # AI indicator (green circle) - top right
    ai_color = (76, 175, 80, 255)  # #4CAF50
    ai_radius = size // 12
    ai_x = size - margin - ai_radius - size // 20
    ai_y = margin + ai_radius + size // 20
    
    draw.ellipse(
        [ai_x - ai_radius, ai_y - ai_radius, ai_x + ai_radius, ai_y + ai_radius],
        fill=ai_color
    )
    
    # AI text indicator (white) - simplified
    ai_text_width = ai_radius // 1.5
    ai_text_height = ai_radius // 3
    
    draw.rounded_rectangle(
        [ai_x - ai_text_width//2, ai_y - ai_text_height//2, 
         ai_x + ai_text_width//2, ai_y + ai_text_height//2],
        radius=ai_text_height//4,
        fill=(255, 255, 255, 255)
    )
    
    # Small plus signs for medical theme (bottom corners)
    plus_size = size // 20
    plus_width = size // 40
    plus_color = (255, 255, 255, 200)  # Semi-transparent white
    
    # Bottom left plus
    plus_x = margin + size // 8
    plus_y = size - margin - size // 6
    
    # Vertical line
    draw.rectangle(
        [plus_x - plus_width//2, plus_y - plus_size//2,
         plus_x + plus_width//2, plus_y + plus_size//2],
        fill=plus_color
    )
    # Horizontal line
    draw.rectangle(
        [plus_x - plus_size//2, plus_y - plus_width//2,
         plus_x + plus_size//2, plus_y + plus_width//2],
        fill=plus_color
    )
    
    # Bottom right plus
    plus_x = size - margin - size // 8
    
    # Vertical line
    draw.rectangle(
        [plus_x - plus_width//2, plus_y - plus_size//2,
         plus_x + plus_width//2, plus_y + plus_size//2],
        fill=plus_color
    )
    # Horizontal line
    draw.rectangle(
        [plus_x - plus_size//2, plus_y - plus_width//2,
         plus_x + plus_size//2, plus_y + plus_width//2],
        fill=plus_color
    )
    
    return img

def create_android_icons():
    """Create Android app icons in all required sizes"""
    android_sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192
    }
    
    print("🤖 Creating Android app icons...")
    
    for folder, size in android_sizes.items():
        # Create directory if it doesn't exist
        icon_dir = f"android/app/src/main/res/{folder}"
        os.makedirs(icon_dir, exist_ok=True)
        
        # Generate icon
        icon = create_hiv_predictor_icon(size)
        icon_path = f"{icon_dir}/ic_launcher.png"
        icon.save(icon_path, "PNG")
        print(f"✅ Created {icon_path} ({size}x{size})")

def create_web_icons():
    """Create web app icons and favicon"""
    web_sizes = [16, 32, 48, 72, 96, 144, 192, 512]
    
    print("🌐 Creating web app icons...")
    
    # Create directory
    icon_dir = "web/icons"
    os.makedirs(icon_dir, exist_ok=True)
    
    for size in web_sizes:
        icon = create_hiv_predictor_icon(size)
        icon_path = f"{icon_dir}/Icon-{size}.png"
        icon.save(icon_path, "PNG")
        print(f"✅ Created {icon_path} ({size}x{size})")
    
    # Create favicon
    favicon = create_hiv_predictor_icon(32)
    favicon.save("web/favicon.png", "PNG")
    print("✅ Created web/favicon.png")

def main():
    """Generate HIV Predictor app icons"""
    print("🎨 HIV Predictor App - Professional Icon Creator")
    print("=" * 60)
    print("Creating Option 6 (App Icon Style) - Professional medical design")
    print("=" * 60)
    
    try:
        create_android_icons()
        create_web_icons()
        
        print("\n" + "=" * 60)
        print("🎉 HIV Predictor app icons created successfully!")
        print("\n📱 Android icons: android/app/src/main/res/mipmap-*/ic_launcher.png")
        print("🌐 Web icons: web/icons/Icon-*.png")
        print("🌐 Favicon: web/favicon.png")
        print("\n🎨 Icon Features:")
        print("   • Professional blue gradient background")
        print("   • Large white medical cross")
        print("   • Red HIV awareness ribbon")
        print("   • Green AI indicator")
        print("   • Medical plus symbols")
        print("   • Rounded corners for modern look")
        print("\n🔄 Rebuild your app to see the new professional icon!")
        
    except Exception as e:
        print(f"❌ Error creating icons: {e}")
        print("💡 Make sure you have Pillow installed: pip install Pillow")

if __name__ == "__main__":
    main()
