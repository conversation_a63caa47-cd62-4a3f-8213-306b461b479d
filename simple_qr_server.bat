@echo off
echo ========================================
echo   HIV Predictor - QR Code Installation
echo ========================================
echo.

echo 🔍 Checking if APK exists...
if exist "build\app\outputs\flutter-apk\app-debug.apk" (
    echo ✅ APK found!
) else (
    echo ❌ APK not found. Building first...
    flutter build apk --debug
)

echo.
echo 🌐 Starting local web server...
echo 📱 Instructions:
echo    1. Make sure your phone and PC are on same WiFi
echo    2. Open your phone's camera app
echo    3. Scan the QR code that will appear
echo    4. Tap the link to download the app
echo.

REM Install required Python packages
pip install qrcode[pil] --quiet

REM Run the QR server
python qr_install_server.py

pause
