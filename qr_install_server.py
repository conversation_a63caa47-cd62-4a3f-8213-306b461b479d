#!/usr/bin/env python3
"""
QR Code APK Installation Server
Creates a local web server and QR code for easy app installation
"""

import http.server
import socketserver
import qrcode
import os
import webbrowser
from pathlib import Path
import socket

def get_local_ip():
    """Get the local IP address"""
    try:
        # Connect to a remote server to get local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "localhost"

def create_download_page():
    """Create a simple HTML download page"""
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>HIV Predictor App - Download</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .app-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        h1 {
            margin-bottom: 10px;
        }
        .subtitle {
            opacity: 0.8;
            margin-bottom: 30px;
        }
        .download-btn {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s;
        }
        .download-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        .info {
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.7;
        }
        .steps {
            text-align: left;
            margin-top: 20px;
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
        }
        .steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .steps li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="app-icon">🏥</div>
        <h1>HIV Predictor</h1>
        <p class="subtitle">AI-powered HIV risk assessment</p>
        
        <a href="/app-debug.apk" class="download-btn">📱 Download APK</a>
        
        <div class="steps">
            <h3>Installation Steps:</h3>
            <ol>
                <li>Tap "Download APK" above</li>
                <li>Enable "Install unknown apps" in Settings</li>
                <li>Open the downloaded APK file</li>
                <li>Tap "Install" to complete</li>
            </ol>
        </div>
        
        <div class="info">
            <p>✅ English-only interface</p>
            <p>✅ Error-free codebase</p>
            <p>✅ Firebase integration</p>
            <p>✅ Hospital finder & education</p>
        </div>
    </div>
</body>
</html>
"""
    
    with open("download.html", "w", encoding="utf-8") as f:
        f.write(html_content)

def generate_qr_code(url):
    """Generate QR code for the download URL"""
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(url)
    qr.make(fit=True)
    
    img = qr.make_image(fill_color="black", back_color="white")
    img.save("qr_code.png")
    return "qr_code.png"

def main():
    PORT = 8080
    local_ip = get_local_ip()
    
    print("=" * 50)
    print("🎯 HIV Predictor - QR Installation Server")
    print("=" * 50)
    print()
    
    # Create download page
    create_download_page()
    
    # Generate QR code
    download_url = f"http://{local_ip}:{PORT}/download.html"
    qr_file = generate_qr_code(download_url)
    
    print(f"📡 Server starting on: {local_ip}:{PORT}")
    print(f"🌐 Download URL: {download_url}")
    print()
    print("📱 SCAN THIS QR CODE WITH YOUR PHONE:")
    print(f"   QR Code saved as: {qr_file}")
    print()
    print("🔧 Instructions:")
    print("   1. Make sure your phone and PC are on the same WiFi")
    print("   2. Scan the QR code with your phone camera")
    print("   3. Tap the link to open download page")
    print("   4. Download and install the APK")
    print()
    print("⚠️  Note: Make sure app-debug.apk exists in build/app/outputs/flutter-apk/")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 50)
    
    # Open QR code image
    try:
        webbrowser.open(qr_file)
    except:
        pass
    
    # Start server
    os.chdir("build/app/outputs/flutter-apk" if os.path.exists("build/app/outputs/flutter-apk") else ".")
    
    class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            if self.path == "/download.html":
                self.path = "/../../../../../../download.html"
            elif self.path == "/":
                self.path = "/download.html"
            return super().do_GET()
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")

if __name__ == "__main__":
    main()
