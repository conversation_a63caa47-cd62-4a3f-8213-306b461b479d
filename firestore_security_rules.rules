rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions for authentication and authorization
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() &&
             (exists(/databases/$(database)/documents/admins/$(request.auth.uid)) ||
              get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    function isValidUser() {
      return isAuthenticated() && 
             request.auth.token.email_verified == true;
    }
    
    // Users collection - users can only access their own data
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow read: if isAdmin(); // <PERSON><PERSON> can read all user data

      // Allow username lookup for login - TEMPORARY PERMISSIVE RULE
      allow read: if true; // Allow all reads for username lookup during login

      // Allow user creation during registration
      allow create: if isAuthenticated() &&
                       request.auth.uid == userId &&
                       request.resource.data.keys().hasAll(['email', 'createdAt']) &&
                       request.resource.data.email == request.auth.token.email;

      // Allow user creation during registration (unauthenticated)
      allow create: if request.auth == null &&
                       request.resource.data.keys().hasAll(['username', 'email', 'createdAt']);

      // Allow admin to update any user data
      allow write: if isAdmin();

      // Allow admin to query all users for dashboard
      allow list: if isAdmin();
    }
    
    // Predictions collection - users can only access their own predictions
    match /predictions/{predictionId} {
      allow read, write: if isAuthenticated() &&
                            resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() &&
                       request.resource.data.userId == request.auth.uid &&
                       request.resource.data.keys().hasAll(['userId', 'createdAt', 'riskLevel']);
      allow read, write: if isAdmin(); // Admins can read and write all predictions

      // Allow admin to query all predictions
      allow list: if isAdmin();
    }
    
    // User predictions subcollection
    match /users/{userId}/predictions/{predictionId} {
      allow read, write: if isOwner(userId);
      allow read: if isAdmin();
    }
    
    // Analytics collection - read-only for authenticated users, write for system
    match /analytics/{analyticsId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
      allow create: if isAuthenticated(); // Allow users to create analytics events
      allow list: if isAdmin(); // Allow admin to query all analytics
    }
    
    // Notifications collection - users can only access their own notifications
    match /notifications/{notificationId} {
      allow read, write: if isAuthenticated() && 
                            resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && 
                       request.resource.data.userId == request.auth.uid;
      allow write: if isAdmin(); // Admins can create notifications for users
    }
    
    // Admin collection - only admins can access
    match /admins/{adminId} {
      allow read, write: if isAdmin();
      allow list: if isAdmin(); // Allow admin to query all admin documents
    }
    
    // System settings - read-only for authenticated users
    match /settings/{settingId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // App configuration - read-only for all authenticated users
    match /config/{configId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // Hospitals/Healthcare facilities - read-only for authenticated users
    match /hospitals/{hospitalId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // Educational content - read-only for all users (even unauthenticated for education screen)
    match /education/{educationId} {
      allow read: if true; // Public read access for education
      allow write: if isAdmin();
    }
    
    // Feedback collection - authenticated users can create feedback
    match /feedback/{feedbackId} {
      allow create: if isAuthenticated() && 
                       request.resource.data.userId == request.auth.uid;
      allow read: if isAdmin();
    }
    
    // Error logs - system can write, admins can read
    match /error_logs/{logId} {
      allow create: if isAuthenticated();
      allow read: if isAdmin();
    }
    
    // TEMPORARY: More permissive rules for admin dashboard debugging
    match /{document=**} {
      // Allow admin full access for dashboard functionality
      allow read, write, list: if isAdmin();
      // Deny all other access
      allow read, write: if false;
    }
  }
}
