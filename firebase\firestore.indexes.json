{"indexes": [{"collectionGroup": "predictions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "predictions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "riskLevel", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "predictions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "riskLevel", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isGlobal", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "admin_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "adminId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "admin_logs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "action", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "analytics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "analytics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "eventType", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "education_content", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublished", "order": "ASCENDING"}, {"fieldPath": "sortOrder", "order": "ASCENDING"}]}, {"collectionGroup": "education_content", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "sortOrder", "order": "ASCENDING"}]}, {"collectionGroup": "education_content", "queryScope": "COLLECTION", "fields": [{"fieldPath": "language", "order": "ASCENDING"}, {"fieldPath": "sortOrder", "order": "ASCENDING"}]}, {"collectionGroup": "feedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "feedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "hospitals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "hospitals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "symptoms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "sortOrder", "order": "ASCENDING"}]}, {"collectionGroup": "symptoms", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "sortOrder", "order": "ASCENDING"}]}], "fieldOverrides": []}