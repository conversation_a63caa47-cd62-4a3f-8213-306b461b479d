import 'package:flutter/material.dart';
import '../models/prediction_result.dart';
import '../models/user.dart';

class HealthSummaryCard extends StatelessWidget {
  final User user;
  final List<PredictionResult> predictions;

  const HealthSummaryCard({
    super.key,
    required this.user,
    required this.predictions,
  });

  @override
  Widget build(BuildContext context) {
    final healthScore = _calculateHealthScore();
    final riskLevel = _getCurrentRiskLevel();
    final streak = _calculateStreak();
    final improvement = _calculateImprovement();

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getHealthColor(riskLevel),
            _getHealthColor(riskLevel).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: _getHealthColor(riskLevel).withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getHealthIcon(riskLevel),
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Health Summary',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Overall health assessment',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  riskLevel,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Health Score Circle
          Row(
            children: [
              SizedBox(
                width: 80,
                height: 80,
                child: Stack(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),
                    Center(
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white,
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                '${healthScore.round()}',
                                style: TextStyle(
                                  color: _getHealthColor(riskLevel),
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                'Score',
                                style: TextStyle(
                                  color: _getHealthColor(riskLevel),
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 24),
              Expanded(
                child: Column(
                  children: [
                    _buildMetricRow(
                      'Assessments',
                      '${predictions.length}',
                      Icons.assessment,
                    ),
                    const SizedBox(height: 12),
                    _buildMetricRow(
                      'Current Streak',
                      '$streak days',
                      Icons.local_fire_department,
                    ),
                    const SizedBox(height: 12),
                    _buildMetricRow(
                      'Improvement',
                      improvement,
                      improvement.contains('+')
                          ? Icons.trending_up
                          : Icons.trending_down,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Quick Stats
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildQuickStat(
                  'Low Risk',
                  '${_getRiskCount('low')}',
                  Icons.check_circle,
                ),
                _buildQuickStat(
                  'Medium Risk',
                  '${_getRiskCount('medium')}',
                  Icons.warning,
                ),
                _buildQuickStat(
                  'High Risk',
                  '${_getRiskCount('high')}',
                  Icons.error,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.9), size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 12,
            ),
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.9), size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  double _calculateHealthScore() {
    if (predictions.isEmpty) return 50.0;

    double totalScore = 0;
    for (final prediction in predictions) {
      final risk = prediction.riskLevel.toLowerCase();
      if (risk.contains('low')) {
        totalScore += 85;
      } else if (risk.contains('medium')) {
        totalScore += 60;
      } else {
        totalScore += 30;
      }
    }

    final averageScore = totalScore / predictions.length;

    // Add bonus for consistency
    final streak = _calculateStreak();
    final consistencyBonus = (streak * 0.5).clamp(0, 15);

    return (averageScore + consistencyBonus).clamp(0, 100);
  }

  String _getCurrentRiskLevel() {
    if (predictions.isEmpty) return 'Unknown';
    return predictions.first.riskLevel;
  }

  int _calculateStreak() {
    if (predictions.isEmpty) return 0;

    final now = DateTime.now();
    int streak = 0;

    for (final prediction in predictions) {
      final daysDiff = now.difference(prediction.createdAt).inDays;
      if (daysDiff <= streak + 1) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  String _calculateImprovement() {
    if (predictions.length < 2) return 'N/A';

    final latest = predictions.first.riskLevel.toLowerCase();
    final previous = predictions[1].riskLevel.toLowerCase();

    final latestScore = _getRiskScore(latest);
    final previousScore = _getRiskScore(previous);

    final improvement = latestScore - previousScore;

    if (improvement > 0) {
      return '+${improvement * 10}%';
    } else if (improvement < 0) {
      return '${improvement * 10}%';
    } else {
      return '0%';
    }
  }

  int _getRiskScore(String riskLevel) {
    if (riskLevel.contains('low')) return 3;
    if (riskLevel.contains('medium')) return 2;
    return 1;
  }

  int _getRiskCount(String riskType) {
    return predictions
        .where(
          (p) => p.riskLevel.toLowerCase().contains(riskType.toLowerCase()),
        )
        .length;
  }

  Color _getHealthColor(String riskLevel) {
    final risk = riskLevel.toLowerCase();
    if (risk.contains('low')) {
      return const Color(0xFF00C851); // New vibrant green for low risk
    } else if (risk.contains('medium')) {
      return const Color(0xFFFFB300); // New amber for medium risk
    } else {
      return const Color(0xFFFF3547); // New bright red for high risk
    }
  }

  IconData _getHealthIcon(String riskLevel) {
    final risk = riskLevel.toLowerCase();
    if (risk.contains('low')) {
      return Icons.favorite;
    } else if (risk.contains('medium')) {
      return Icons.warning;
    } else {
      return Icons.error;
    }
  }
}
