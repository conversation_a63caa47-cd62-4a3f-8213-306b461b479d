<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HIV Predictor App - QR Download</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            text-align: center;
        }
        
        .app-icon {
            font-size: 80px;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .subtitle {
            opacity: 0.9;
            margin-bottom: 30px;
            font-size: 1.1em;
        }
        
        .qr-container {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin: 30px 0;
            display: inline-block;
        }
        
        #qrcode {
            margin: 0 auto;
        }
        
        .download-section {
            margin: 30px 0;
        }
        
        .download-btn {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .download-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 30px 0;
            text-align: left;
        }
        
        .info-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
        }
        
        .steps {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
        }
        
        .steps h3 {
            margin-top: 0;
            color: #FFD700;
        }
        
        .steps ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .steps li {
            margin: 8px 0;
            line-height: 1.4;
        }
        
        .url-display {
            background: rgba(0, 0, 0, 0.2);
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            margin: 15px 0;
            word-break: break-all;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid #4CAF50;
        }
        
        .status.warning {
            background: rgba(255, 193, 7, 0.3);
            border: 1px solid #FFC107;
            color: #FFF3CD;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="app-icon">🏥</div>
        <h1>HIV Predictor</h1>
        <p class="subtitle">AI-powered HIV risk assessment and healthcare navigation</p>
        
        <div class="status success">
            ✅ App ready for installation via QR code
        </div>
        
        <div class="qr-container">
            <div id="qrcode"></div>
        </div>
        
        <div class="download-section">
            <a href="#" id="downloadLink" class="download-btn">📱 Direct Download</a>
        </div>
        
        <div class="url-display" id="downloadUrl">
            Generating download URL...
        </div>
        
        <div class="steps">
            <h3>📱 Installation Steps:</h3>
            <ol>
                <li><strong>Scan QR Code:</strong> Use your phone's camera app to scan the QR code above</li>
                <li><strong>Open Link:</strong> Tap the notification or link that appears</li>
                <li><strong>Enable Installation:</strong> Go to Settings > Security > "Install unknown apps" and enable for your browser</li>
                <li><strong>Download APK:</strong> Tap "Direct Download" on the page that opens</li>
                <li><strong>Install App:</strong> Open the downloaded APK file and tap "Install"</li>
            </ol>
        </div>
        
        <div class="info-grid">
            <div class="info-item">
                <strong>✅ Features:</strong><br>
                • English-only interface<br>
                • 22 enhanced symptoms<br>
                • Hospital finder with maps
            </div>
            <div class="info-item">
                <strong>🔧 Requirements:</strong><br>
                • Android 6.0+ (API 23)<br>
                • Same WiFi network<br>
                • Unknown sources enabled
            </div>
            <div class="info-item">
                <strong>🛡️ Security:</strong><br>
                • Error-free codebase<br>
                • Firebase integration<br>
                • Local network only
            </div>
            <div class="info-item">
                <strong>📊 App Info:</strong><br>
                • Version: 1.0.0<br>
                • Size: ~15-20 MB<br>
                • Debug build
            </div>
        </div>
        
        <div class="status warning">
            ⚠️ Make sure your phone and PC are connected to the same WiFi network
        </div>
    </div>

    <script>
        // Get local IP and generate QR code
        function generateQRCode() {
            // Try to get local IP (this is a simplified approach)
            const hostname = window.location.hostname;
            const port = window.location.port || '8080';
            const downloadUrl = `http://${hostname}:${port}/app-debug.apk`;
            
            // Update download link and URL display
            document.getElementById('downloadLink').href = downloadUrl;
            document.getElementById('downloadUrl').textContent = downloadUrl;
            
            // Generate QR code
            QRCode.toCanvas(document.getElementById('qrcode'), downloadUrl, {
                width: 200,
                height: 200,
                colorDark: '#000000',
                colorLight: '#ffffff',
                margin: 2
            }, function (error) {
                if (error) {
                    console.error('QR Code generation failed:', error);
                    document.getElementById('qrcode').innerHTML = '<p style="color: red;">QR Code generation failed</p>';
                } else {
                    console.log('QR Code generated successfully!');
                }
            });
        }
        
        // Generate QR code when page loads
        window.addEventListener('load', generateQRCode);
    </script>
</body>
</html>
