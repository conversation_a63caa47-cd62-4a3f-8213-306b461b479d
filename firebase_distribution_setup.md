# Firebase App Distribution with QR Code

## Method 2: Professional App Distribution

### Step 1: Install Firebase CLI
```bash
npm install -g firebase-tools
firebase login
```

### Step 2: Initialize App Distribution
```bash
firebase init appdistribution
```

### Step 3: Upload APK and Generate QR Code
```bash
# Upload APK to Firebase
firebase appdistribution:distribute build/app/outputs/flutter-apk/app-debug.apk \
  --app YOUR_FIREBASE_APP_ID \
  --groups "testers" \
  --release-notes "HIV Predictor v1.0 - English only, error-free version"

# This will generate a QR code and download link
```

### Benefits:
- ✅ Professional distribution
- ✅ Automatic QR code generation
- ✅ Version management
- ✅ Analytics and crash reporting
- ✅ Secure distribution

## Method 3: GitHub Releases with QR Code

### Step 1: Upload to GitHub Releases
1. Create a new release on GitHub
2. Upload the APK file as an asset
3. Get the download URL

### Step 2: Generate QR Code
Use any QR code generator with the GitHub release URL

### Step 3: Share QR Code
Users scan the QR code to download directly from GitHub

## Method 4: Google Drive/Dropbox with QR Code

### Step 1: Upload APK
1. Upload app-debug.apk to Google Drive or Dropbox
2. Get shareable link
3. Make sure link allows direct download

### Step 2: Generate QR Code
Create QR code with the shareable link

### Step 3: Installation
1. Scan QR code
2. Download APK
3. Install on device
