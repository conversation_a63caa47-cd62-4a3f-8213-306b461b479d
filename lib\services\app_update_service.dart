import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppUpdateService {
  static const String _updateCheckUrl = 'http://172.30.18.51:8080/version.json';
  static const String _downloadUrl = 'http://172.30.18.51:8080/app-debug.apk';
  
  /// Check if app update is available
  static Future<UpdateInfo?> checkForUpdate() async {
    try {
      // Get current app version
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;
      final currentBuildNumber = int.parse(packageInfo.buildNumber);
      
      if (kDebugMode) {
        print('🔄 Current version: $currentVersion ($currentBuildNumber)');
      }
      
      // Check server for latest version
      final response = await http.get(Uri.parse(_updateCheckUrl));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final latestVersion = data['version'] as String;
        final latestBuildNumber = data['buildNumber'] as int;
        final updateUrl = data['downloadUrl'] as String? ?? _downloadUrl;
        final releaseNotes = data['releaseNotes'] as String? ?? 'Bug fixes and improvements';
        final isForced = data['forceUpdate'] as bool? ?? false;
        
        if (kDebugMode) {
          print('🔄 Latest version: $latestVersion ($latestBuildNumber)');
        }
        
        // Compare versions
        if (latestBuildNumber > currentBuildNumber) {
          return UpdateInfo(
            currentVersion: currentVersion,
            latestVersion: latestVersion,
            currentBuildNumber: currentBuildNumber,
            latestBuildNumber: latestBuildNumber,
            downloadUrl: updateUrl,
            releaseNotes: releaseNotes,
            isForced: isForced,
          );
        }
      }
      
      return null; // No update available
    } catch (e) {
      if (kDebugMode) {
        print('🔄 Update check failed: $e');
      }
      return null;
    }
  }
  
  /// Download and install update (Android)
  static Future<void> downloadUpdate(String downloadUrl) async {
    try {
      final uri = Uri.parse(downloadUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔄 Download failed: $e');
      }
    }
  }
  
  /// Show update dialog
  static Future<bool> showUpdateDialog({
    required String currentVersion,
    required String latestVersion,
    required String releaseNotes,
    required bool isForced,
  }) async {
    // This would be implemented in the UI layer
    // Return true if user wants to update
    return true;
  }
}

class UpdateInfo {
  final String currentVersion;
  final String latestVersion;
  final int currentBuildNumber;
  final int latestBuildNumber;
  final String downloadUrl;
  final String releaseNotes;
  final bool isForced;
  
  UpdateInfo({
    required this.currentVersion,
    required this.latestVersion,
    required this.currentBuildNumber,
    required this.latestBuildNumber,
    required this.downloadUrl,
    required this.releaseNotes,
    required this.isForced,
  });
  
  bool get isUpdateAvailable => latestBuildNumber > currentBuildNumber;
  
  String get updateMessage {
    if (isForced) {
      return 'A critical update is required to continue using the app.';
    } else {
      return 'A new version of HIV Predictor is available with improvements and new features.';
    }
  }
}

/// Auto-update checker mixin for screens
mixin AutoUpdateChecker {
  Future<void> checkForUpdates() async {
    final updateInfo = await AppUpdateService.checkForUpdate();
    
    if (updateInfo != null && updateInfo.isUpdateAvailable) {
      // Show update notification
      _showUpdateNotification(updateInfo);
    }
  }
  
  void _showUpdateNotification(UpdateInfo updateInfo) {
    // Implementation would be in the specific screen
    if (kDebugMode) {
      print('🔄 Update available: ${updateInfo.latestVersion}');
    }
  }
}
