rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    function isValidImageFile() {
      return resource.contentType.matches('image/.*') &&
             resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    
    function isValidDocumentFile() {
      return resource.contentType.matches('application/pdf') ||
             resource.contentType.matches('text/.*') ||
             resource.contentType.matches('application/.*csv') &&
             resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    // User profile images
    match /users/{userId}/profile/{imageId} {
      // Users can read and write their own profile images
      allow read, write: if isAuthenticated() && 
                            isOwner(userId) && 
                            isValidImageFile();
      
      // <PERSON><PERSON> can read all profile images
      allow read: if isAdmin();
    }
    
    // Education content images
    match /education/{contentId}/{imageId} {
      // Anyone authenticated can read education images
      allow read: if isAuthenticated();
      
      // Only admins can upload education images
      allow write: if isAdmin() && isValidImageFile();
    }
    
    // Data exports (admin only)
    match /exports/{exportId} {
      // Only admins can read and write export files
      allow read, write: if isAdmin() && isValidDocumentFile();
    }
    
    // Temporary uploads
    match /temp/{userId}/{fileId} {
      // Users can upload temporary files
      allow read, write: if isAuthenticated() && 
                            isOwner(userId) && 
                            (isValidImageFile() || isValidDocumentFile());
      
      // Auto-delete after 24 hours (handled by Cloud Functions)
    }
    
    // System backups (admin only)
    match /backups/{backupId} {
      // Only admins can access backups
      allow read, write: if isAdmin();
    }
    
    // Default deny rule
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
