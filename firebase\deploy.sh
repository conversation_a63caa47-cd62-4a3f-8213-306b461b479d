#!/bin/bash

# HIV Predictor App - Firebase Deployment Script
# This script deploys all Firebase services for the HIV Predictor App

echo "🔥 Starting Firebase deployment for HIV Predictor App..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "🔐 Please login to Firebase first:"
    firebase login
fi

# Set the project (replace with your actual project ID)
echo "📋 Setting Firebase project..."
read -p "Enter your Firebase project ID: " PROJECT_ID
firebase use $PROJECT_ID

# Deploy Firestore rules
echo "📜 Deploying Firestore security rules..."
firebase deploy --only firestore:rules

# Deploy Firestore indexes
echo "📊 Deploying Firestore indexes..."
firebase deploy --only firestore:indexes

# Deploy Storage rules
echo "💾 Deploying Storage security rules..."
firebase deploy --only storage

# Build and deploy Cloud Functions
echo "⚡ Building and deploying Cloud Functions..."
cd functions
npm install
npm run build
cd ..
firebase deploy --only functions

# Deploy hosting (if web app is built)
if [ -d "build/web" ]; then
    echo "🌐 Deploying web hosting..."
    firebase deploy --only hosting
else
    echo "⚠️  Web build not found. Skipping hosting deployment."
    echo "   Run 'flutter build web' first if you want to deploy hosting."
fi

echo "✅ Firebase deployment completed successfully!"
echo ""
echo "🔗 Your Firebase console: https://console.firebase.google.com/project/$PROJECT_ID"
echo "📱 Your web app: https://$PROJECT_ID.web.app"
echo ""
echo "📋 Next steps:"
echo "1. Update your Flutter app's firebase_options.dart with the correct configuration"
echo "2. Test your app with the deployed Firebase services"
echo "3. Set up monitoring and alerts in the Firebase console"
echo ""
echo "🔒 Security checklist:"
echo "- ✓ Firestore security rules deployed"
echo "- ✓ Storage security rules deployed"
echo "- ✓ Cloud Functions with authentication deployed"
echo "- ⚠️  Remember to configure your domain for production"
echo "- ⚠️  Set up backup and monitoring"
