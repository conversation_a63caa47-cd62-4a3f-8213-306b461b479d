import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import * as express from "express";
import * as cors from "cors";
import * as helmet from "helmet";
import { RateLimiterMemory } from "rate-limiter-flexible";
import * as cron from "node-cron";
import { createObjectCsvWriter } from "csv-writer";
import { Parser } from "json2csv";
import * as nodemailer from "nodemailer";

// Initialize Firebase Admin
admin.initializeApp();
const db = admin.firestore();
const auth = admin.auth();

// Initialize Express app
const app = express();

// Security middleware
app.use(helmet());
app.use(cors({ origin: true }));
app.use(express.json({ limit: "10mb" }));

// Rate limiting
const rateLimiter = new RateLimiterMemory({
  keyGenerator: (req) => req.ip,
  points: 100, // Number of requests
  duration: 60, // Per 60 seconds
});

// Rate limiting middleware
const rateLimitMiddleware = async (req: any, res: any, next: any) => {
  try {
    await rateLimiter.consume(req.ip);
    next();
  } catch (rejRes) {
    res.status(429).send("Too Many Requests");
  }
};

app.use(rateLimitMiddleware);

// Authentication middleware
const authenticateUser = async (req: any, res: any, next: any) => {
  try {
    const token = req.headers.authorization?.split("Bearer ")[1];
    if (!token) {
      return res.status(401).json({ error: "No token provided" });
    }

    const decodedToken = await auth.verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    res.status(401).json({ error: "Invalid token" });
  }
};

// Admin middleware
const requireAdmin = async (req: any, res: any, next: any) => {
  try {
    const userDoc = await db.collection("users").doc(req.user.uid).get();
    const userData = userDoc.data();

    if (!userData?.isAdmin) {
      return res.status(403).json({ error: "Admin access required" });
    }

    next();
  } catch (error) {
    res.status(500).json({ error: "Error checking admin status" });
  }
};

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
  });
});

// Export user data (Admin only)
app.get(
  "/admin/export/users",
  authenticateUser,
  requireAdmin,
  async (req, res) => {
    try {
      const usersSnapshot = await db.collection("users").get();
      const users = usersSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || null,
        lastLoginAt: doc.data().lastLoginAt?.toDate?.()?.toISOString() || null,
      }));

      const format = (req.query.format as string) || "json";

      if (format === "csv") {
        const parser = new Parser();
        const csv = parser.parse(users);
        res.setHeader("Content-Type", "text/csv");
        res.setHeader("Content-Disposition", "attachment; filename=users.csv");
        res.send(csv);
      } else {
        res.setHeader("Content-Type", "application/json");
        res.setHeader("Content-Disposition", "attachment; filename=users.json");
        res.json(users);
      }

      // Log admin action
      await db.collection("admin_logs").add({
        adminId: req.user.uid,
        action: "export_users",
        targetResource: "users",
        metadata: {
          format,
          recordCount: users.length,
        },
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        result: "success",
        description: `Exported ${users.length} users in ${format} format`,
      });
    } catch (error) {
      console.error("Error exporting users:", error);
      res.status(500).json({ error: "Failed to export users" });
    }
  }
);

// Export predictions data (Admin only)
app.get(
  "/admin/export/predictions",
  authenticateUser,
  requireAdmin,
  async (req, res) => {
    try {
      const predictionsSnapshot = await db.collection("predictions").get();
      const usersSnapshot = await db.collection("users").get();

      // Create user lookup map
      const userMap = new Map();
      usersSnapshot.docs.forEach((doc) => {
        userMap.set(doc.id, doc.data());
      });

      const predictions = predictionsSnapshot.docs.map((doc) => {
        const data = doc.data();
        const user = userMap.get(data.userId);
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || null,
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || null,
          username: user?.username || "Unknown",
          userEmail: user?.email || "Unknown",
          userFullName: user?.fullName || "Unknown",
        };
      });

      const format = (req.query.format as string) || "json";

      if (format === "csv") {
        const parser = new Parser();
        const csv = parser.parse(predictions);
        res.setHeader("Content-Type", "text/csv");
        res.setHeader(
          "Content-Disposition",
          "attachment; filename=predictions.csv"
        );
        res.send(csv);
      } else {
        res.setHeader("Content-Type", "application/json");
        res.setHeader(
          "Content-Disposition",
          "attachment; filename=predictions.json"
        );
        res.json(predictions);
      }

      // Log admin action
      await db.collection("admin_logs").add({
        adminId: req.user.uid,
        action: "export_predictions",
        targetResource: "predictions",
        metadata: {
          format,
          recordCount: predictions.length,
        },
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        result: "success",
        description: `Exported ${predictions.length} predictions in ${format} format`,
      });
    } catch (error) {
      console.error("Error exporting predictions:", error);
      res.status(500).json({ error: "Failed to export predictions" });
    }
  }
);

// Export all data (Admin only)
app.get(
  "/admin/export/all",
  authenticateUser,
  requireAdmin,
  async (req, res) => {
    try {
      const [
        usersSnapshot,
        predictionsSnapshot,
        symptomsSnapshot,
        hospitalsSnapshot,
      ] = await Promise.all([
        db.collection("users").get(),
        db.collection("predictions").get(),
        db.collection("symptoms").get(),
        db.collection("hospitals").get(),
      ]);

      const exportData = {
        users: usersSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || null,
          lastLoginAt:
            doc.data().lastLoginAt?.toDate?.()?.toISOString() || null,
        })),
        predictions: predictionsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || null,
          updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || null,
        })),
        symptoms: symptomsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })),
        hospitals: hospitalsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })),
        exportInfo: {
          timestamp: new Date().toISOString(),
          totalRecords:
            usersSnapshot.size +
            predictionsSnapshot.size +
            symptomsSnapshot.size +
            hospitalsSnapshot.size,
        },
      };

      res.setHeader("Content-Type", "application/json");
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=hiv_predictor_full_export.json"
      );
      res.json(exportData);

      // Log admin action
      await db.collection("admin_logs").add({
        adminId: req.user.uid,
        action: "export_all_data",
        targetResource: "all_collections",
        metadata: {
          totalRecords: exportData.exportInfo.totalRecords,
          collections: ["users", "predictions", "symptoms", "hospitals"],
        },
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        result: "success",
        description: `Exported all data (${exportData.exportInfo.totalRecords} total records)`,
      });
    } catch (error) {
      console.error("Error exporting all data:", error);
      res.status(500).json({ error: "Failed to export all data" });
    }
  }
);

// Get system statistics (Admin only)
app.get("/admin/stats", authenticateUser, requireAdmin, async (req, res) => {
  try {
    const [usersSnapshot, predictionsSnapshot, highRiskSnapshot] =
      await Promise.all([
        db.collection("users").get(),
        db.collection("predictions").get(),
        db
          .collection("predictions")
          .where("riskLevel", "==", "High Risk")
          .get(),
      ]);

    const stats = {
      totalUsers: usersSnapshot.size,
      totalPredictions: predictionsSnapshot.size,
      highRiskPredictions: highRiskSnapshot.size,
      timestamp: new Date().toISOString(),
    };

    res.json(stats);
  } catch (error) {
    console.error("Error getting stats:", error);
    res.status(500).json({ error: "Failed to get statistics" });
  }
});

// Expose Express API as a single Cloud Function
export const api = functions.https.onRequest(app);

// Cloud Function: User signup trigger
export const onUserCreate = functions.auth.user().onCreate(async (user) => {
  try {
    // Create user document in Firestore
    await db
      .collection("users")
      .doc(user.uid)
      .set({
        email: user.email,
        fullName: user.displayName || "",
        username: user.email?.split("@")[0] || "",
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        lastLoginAt: admin.firestore.FieldValue.serverTimestamp(),
        isAdmin: false,
        emailVerified: user.emailVerified,
      });

    console.log(`User document created for ${user.uid}`);
  } catch (error) {
    console.error("Error creating user document:", error);
  }
});

// Cloud Function: User deletion trigger
export const onUserDelete = functions.auth.user().onDelete(async (user) => {
  try {
    // Delete user document
    await db.collection("users").doc(user.uid).delete();

    // Delete user predictions
    const predictionsSnapshot = await db
      .collection("predictions")
      .where("userId", "==", user.uid)
      .get();

    const batch = db.batch();
    predictionsSnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });
    await batch.commit();

    console.log(`User data deleted for ${user.uid}`);
  } catch (error) {
    console.error("Error deleting user data:", error);
  }
});

// Cloud Function: High-risk prediction trigger
export const onHighRiskPrediction = functions.firestore
  .document("predictions/{predictionId}")
  .onCreate(async (snap, context) => {
    const prediction = snap.data();

    if (prediction.riskLevel === "High Risk") {
      try {
        // Get user data
        const userDoc = await db
          .collection("users")
          .doc(prediction.userId)
          .get();
        const userData = userDoc.data();

        // Create notification for user
        await db.collection("notifications").add({
          userId: prediction.userId,
          type: "high_risk_alert",
          title: "High Risk Assessment",
          message:
            "Your recent assessment indicates high risk. Please seek medical attention immediately.",
          riskLevel: "High Risk",
          data: {
            predictionId: context.params.predictionId,
            actionRequired: true,
          },
          isRead: false,
          isGlobal: false,
          priority: "urgent",
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        });

        // Create admin notification
        const adminsSnapshot = await db
          .collection("users")
          .where("isAdmin", "==", true)
          .get();

        const adminNotifications = adminsSnapshot.docs.map((adminDoc) => ({
          userId: adminDoc.id,
          type: "admin_alert",
          title: "High Risk Case Detected",
          message: `User ${
            userData?.username || "Unknown"
          } has a high-risk assessment.`,
          riskLevel: "High Risk",
          data: {
            predictionId: context.params.predictionId,
            targetUserId: prediction.userId,
            username: userData?.username,
          },
          isRead: false,
          isGlobal: false,
          priority: "high",
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        }));

        // Batch create admin notifications
        const batch = db.batch();
        adminNotifications.forEach((notification) => {
          const notificationRef = db.collection("notifications").doc();
          batch.set(notificationRef, notification);
        });
        await batch.commit();

        console.log(
          `High-risk notifications created for prediction ${context.params.predictionId}`
        );
      } catch (error) {
        console.error("Error handling high-risk prediction:", error);
      }
    }
  });

// Cloud Function: Daily cleanup
export const dailyCleanup = functions.pubsub
  .schedule("0 2 * * *") // Run at 2 AM daily
  .timeZone("UTC")
  .onRun(async (context) => {
    try {
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Clean up expired notifications
      const expiredNotifications = await db
        .collection("notifications")
        .where("expiresAt", "<=", now)
        .get();

      const batch = db.batch();
      expiredNotifications.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      // Clean up old analytics events (older than 30 days)
      const oldAnalytics = await db
        .collection("analytics")
        .where("timestamp", "<=", thirtyDaysAgo)
        .get();

      oldAnalytics.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();

      console.log(
        `Cleanup completed: ${expiredNotifications.size} notifications, ${oldAnalytics.size} analytics events`
      );
    } catch (error) {
      console.error("Error in daily cleanup:", error);
    }
  });

// Cloud Function: Weekly statistics report
export const weeklyStatsReport = functions.pubsub
  .schedule("0 9 * * 1") // Run at 9 AM every Monday
  .timeZone("UTC")
  .onRun(async (context) => {
    try {
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      // Get weekly statistics
      const [
        totalUsers,
        weeklyPredictions,
        highRiskPredictions,
        mediumRiskPredictions,
        lowRiskPredictions,
      ] = await Promise.all([
        db.collection("users").get(),
        db.collection("predictions").where("createdAt", ">=", oneWeekAgo).get(),
        db
          .collection("predictions")
          .where("createdAt", ">=", oneWeekAgo)
          .where("riskLevel", "==", "High Risk")
          .get(),
        db
          .collection("predictions")
          .where("createdAt", ">=", oneWeekAgo)
          .where("riskLevel", "==", "Medium Risk")
          .get(),
        db
          .collection("predictions")
          .where("createdAt", ">=", oneWeekAgo)
          .where("riskLevel", "==", "Low Risk")
          .get(),
      ]);

      const stats = {
        totalUsers: totalUsers.size,
        weeklyPredictions: weeklyPredictions.size,
        highRisk: highRiskPredictions.size,
        mediumRisk: mediumRiskPredictions.size,
        lowRisk: lowRiskPredictions.size,
        weekStart: oneWeekAgo.toISOString(),
        weekEnd: now.toISOString(),
      };

      // Store weekly report
      await db.collection("weekly_reports").add({
        ...stats,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      // Send notification to admins
      const adminsSnapshot = await db
        .collection("users")
        .where("isAdmin", "==", true)
        .get();

      const adminNotifications = adminsSnapshot.docs.map((adminDoc) => ({
        userId: adminDoc.id,
        type: "weekly_report",
        title: "Weekly Statistics Report",
        message: `Weekly report: ${stats.weeklyPredictions} assessments, ${stats.highRisk} high-risk cases`,
        data: stats,
        isRead: false,
        isGlobal: false,
        priority: "medium",
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days
      }));

      const batch = db.batch();
      adminNotifications.forEach((notification) => {
        const notificationRef = db.collection("notifications").doc();
        batch.set(notificationRef, notification);
      });
      await batch.commit();

      console.log("Weekly statistics report generated:", stats);
    } catch (error) {
      console.error("Error generating weekly report:", error);
    }
  });
