import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/firebase_auth_service.dart';
import '../services/firebase_database_service.dart';
import '../models/prediction_result.dart';
import '../models/user.dart';
import '../widgets/dashboard_widgets.dart';
import '../widgets/achievement_system.dart';
import '../widgets/smart_notifications.dart';
import '../widgets/health_chart_widget.dart';
import '../widgets/health_insights_widget.dart';
import '../widgets/health_summary_card.dart';
import '../widgets/dashboard_kpi_widget.dart';
import 'home_screen.dart';
import 'login_screen.dart';
import 'prediction_history_screen.dart';
import 'beautiful_education_screen.dart';

class EnhancedDashboardScreen extends StatefulWidget {
  const EnhancedDashboardScreen({super.key});

  @override
  State<EnhancedDashboardScreen> createState() =>
      _EnhancedDashboardScreenState();
}

class _EnhancedDashboardScreenState extends State<EnhancedDashboardScreen>
    with TickerProviderStateMixin {
  late FirebaseAuthService _authService;
  late FirebaseDatabaseService _databaseService;

  User? _currentUser;
  List<PredictionResult> _recentPredictions = [];
  bool _isLoading = true;
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _authService = Provider.of<FirebaseAuthService>(context, listen: false);
    _databaseService = Provider.of<FirebaseDatabaseService>(
      context,
      listen: false,
    );
    _initializeAnimations();
    _loadUserData();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
          ),
        );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _animationController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      final user = await _authService.getCurrentUser();
      if (user != null) {
        final predictions = await _databaseService.getUserPredictions();
        if (mounted) {
          setState(() {
            _currentUser = user;
            _recentPredictions = predictions;
            _isLoading = false;
          });
        }
      } else {
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const LoginScreen()),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('Error loading data: ${e.toString()}');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Future<void> _signOut() async {
    try {
      await _authService.signOut();
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    } catch (e) {
      _showErrorSnackBar('Error signing out: ${e.toString()}');
    }
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  }

  String _getHealthStatus() {
    if (_recentPredictions.isEmpty) return 'No Data';

    final latestPrediction = _recentPredictions.first;
    final riskLevel = latestPrediction.riskLevel.toLowerCase();

    if (riskLevel.contains('low')) return 'Excellent';
    if (riskLevel.contains('medium')) return 'Good';
    return 'Needs Attention';
  }

  Color _getHealthStatusColor() {
    final status = _getHealthStatus();
    switch (status) {
      case 'Excellent':
        return const Color(0xFF00C851); // New vibrant green for low risk
      case 'Good':
        return const Color(0xFF00C851); // New vibrant green for low risk
      case 'Needs Attention':
        return const Color(0xFFFF3547); // New bright red for high risk
      default:
        return Colors.grey;
    }
  }

  IconData _getWeatherIcon() {
    final status = _getHealthStatus();
    switch (status) {
      case 'Excellent':
        return Icons.wb_sunny;
      case 'Good':
        return Icons.wb_cloudy;
      case 'Needs Attention':
        return Icons.cloud;
      default:
        return Icons.help_outline;
    }
  }

  String _getHealthDescription() {
    final status = _getHealthStatus();
    switch (status) {
      case 'Excellent':
        return 'Your health is looking great! Keep up the good work.';
      case 'Good':
        return 'You\'re doing well. Consider regular checkups.';
      case 'Needs Attention':
        return 'Consider consulting a healthcare professional.';
      default:
        return 'Take an assessment to see your health status.';
    }
  }

  int _getHealthStreak() {
    if (_recentPredictions.isEmpty) return 0;

    int streak = 0;
    DateTime? lastDate;

    for (final prediction in _recentPredictions.reversed) {
      final currentDate = DateTime(
        prediction.createdAt.year,
        prediction.createdAt.month,
        prediction.createdAt.day,
      );

      if (lastDate == null) {
        streak = 1;
        lastDate = currentDate;
      } else {
        final difference = currentDate.difference(lastDate).inDays;
        if (difference == 1) {
          streak++;
          lastDate = currentDate;
        } else {
          break;
        }
      }
    }

    return streak;
  }

  double _getHealthScore() {
    if (_recentPredictions.isEmpty) return 0.0;

    final recent = _recentPredictions.take(5);
    int totalScore = 0;

    for (final prediction in recent) {
      final riskLevel = prediction.riskLevel.toLowerCase();
      if (riskLevel.contains('low')) {
        totalScore += 100;
      } else if (riskLevel.contains('medium')) {
        totalScore += 70;
      } else {
        totalScore += 40;
      }
    }

    return (totalScore / (recent.length * 100)).clamp(0.0, 1.0);
  }

  String _getDailyQuote() {
    final quotes = [
      {
        'quote':
            'Health is not about the weight you lose, but about the life you gain.',
        'author': 'Dr. Josh Axe',
      },
      {
        'quote':
            'Take care of your body. It\'s the only place you have to live.',
        'author': 'Jim Rohn',
      },
      {'quote': 'The greatest wealth is health.', 'author': 'Virgil'},
      {
        'quote':
            'Your body can stand almost anything. It\'s your mind you have to convince.',
        'author': 'Unknown',
      },
      {
        'quote':
            'Health is a state of complete harmony of the body, mind and spirit.',
        'author': 'B.K.S. Iyengar',
      },
      {
        'quote': 'Prevention is better than cure.',
        'author': 'Desiderius Erasmus',
      },
      {
        'quote': 'A healthy outside starts from the inside.',
        'author': 'Robert Urich',
      },
    ];

    final now = DateTime.now();
    final quoteIndex = now.day % quotes.length;
    return '${quotes[quoteIndex]['quote']!}|${quotes[quoteIndex]['author']!}';
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF1976D2), Color(0xFF42A5F5)],
            ),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(color: Colors.white),
                SizedBox(height: 16),
                Text(
                  'Loading your health dashboard...',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (_currentUser == null) {
      return const LoginScreen();
    }

    final quoteData = _getDailyQuote().split('|');
    final quote = quoteData[0];
    final author = quoteData[1];

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: CustomScrollView(
            slivers: [
              _buildEnhancedAppBar(),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Health Summary Card
                      HealthSummaryCard(
                        user: _currentUser!,
                        predictions: _recentPredictions,
                      ),
                      const SizedBox(height: 24),

                      _buildHealthWeatherCard(),
                      const SizedBox(height: 24),
                      _buildStatsGrid(),
                      const SizedBox(height: 24),

                      // KPI Dashboard Section
                      DashboardKPIWidget(
                        user: _currentUser!,
                        predictions: _recentPredictions,
                      ),
                      const SizedBox(height: 24),
                      _buildQuickActions(),
                      const SizedBox(height: 24),
                      if (_getHealthStreak() > 0) ...[
                        _buildStreakCard(),
                        const SizedBox(height: 24),
                      ],
                      // Smart Notifications Section
                      SmartNotificationWidget(
                        user: _currentUser!,
                        predictions: _recentPredictions,
                      ),
                      const SizedBox(height: 24),

                      // Health Chart Section
                      HealthChartWidget(
                        predictions: _recentPredictions,
                        title: 'Health Trend Analysis',
                      ),
                      const SizedBox(height: 24),

                      // Achievement System Section
                      AchievementWidget(predictions: _recentPredictions),
                      const SizedBox(height: 24),

                      // AI Health Insights Section
                      HealthInsightsWidget(
                        user: _currentUser!,
                        predictions: _recentPredictions,
                      ),
                      const SizedBox(height: 24),

                      DashboardWidgets.buildMotivationalQuote(
                        quote: quote,
                        author: author,
                      ),
                      const SizedBox(height: 24),
                      _buildRecentActivity(),
                      const SizedBox(height: 100), // Bottom padding for FAB
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildEnhancedFAB(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildEnhancedAppBar() {
    return SliverAppBar(
      expandedHeight: 140,
      floating: false,
      pinned: true,
      backgroundColor: const Color(0xFF1976D2),
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF1976D2), Color(0xFF42A5F5)],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      ScaleTransition(
                        scale: _pulseAnimation,
                        child: CircleAvatar(
                          radius: 28,
                          backgroundColor: Colors.white.withValues(alpha: 0.2),
                          child: Text(
                            _currentUser!.fullName.isNotEmpty
                                ? _currentUser!.fullName[0].toUpperCase()
                                : _currentUser!.username[0].toUpperCase(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${_getGreeting()}, ${_currentUser!.fullName.isNotEmpty ? _currentUser!.fullName.split(' ').first : _currentUser!.username}!',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Your health journey continues',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.8),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${(_getHealthScore() * 100).round()}%',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined, color: Colors.white),
          onPressed: () => _showNotificationsDialog(),
          tooltip: 'Notifications',
        ),
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.white),
          onPressed: () {
            setState(() {
              _isLoading = true;
            });
            _loadUserData();
          },
          tooltip: 'Refresh',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: (value) {
            switch (value) {
              case 'profile':
                _showProfileDialog();
                break;
              case 'settings':
                _showSettingsDialog();
                break;
              case 'logout':
                _signOut();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: Row(
                children: [
                  Icon(Icons.person, color: Color(0xFF1976D2)),
                  SizedBox(width: 8),
                  Text('Profile'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings, color: Color(0xFF1976D2)),
                  SizedBox(width: 8),
                  Text('Settings'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Sign Out'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildHealthWeatherCard() {
    return DashboardWidgets.buildHealthWeather(
      status: _getHealthStatus(),
      description: _getHealthDescription(),
      weatherIcon: _getWeatherIcon(),
      color: _getHealthStatusColor(),
    );
  }

  Widget _buildStatsGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Health Overview',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: DashboardWidgets.buildStatCard(
                title: 'Health Score',
                value: '${(_getHealthScore() * 100).round()}%',
                icon: Icons.favorite,
                color: _getHealthStatusColor(),
                subtitle: 'Based on recent assessments',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DashboardWidgets.buildStatCard(
                title: 'Assessments',
                value: '${_recentPredictions.length}',
                icon: Icons.assessment,
                color: const Color(0xFF2196F3),
                subtitle: 'Total completed',
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const PredictionHistoryScreen(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: DashboardWidgets.buildStatCard(
                title: 'Streak',
                value: '${_getHealthStreak()}',
                icon: Icons.local_fire_department,
                color: const Color(0xFFFF6B6B),
                subtitle: 'Days active',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DashboardWidgets.buildStatCard(
                title: 'Last Check',
                value: _getDaysSinceLastAssessment(),
                icon: Icons.schedule,
                color: const Color(0xFF4CAF50),
                subtitle: 'Days ago',
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getDaysSinceLastAssessment() {
    if (_recentPredictions.isEmpty) return 'Never';

    final lastAssessment = _recentPredictions.first.createdAt;
    final daysDiff = DateTime.now().difference(lastAssessment).inDays;

    if (daysDiff == 0) return 'Today';
    if (daysDiff == 1) return '1';
    return '$daysDiff';
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildEnhancedActionCard(
                icon: Icons.add_circle_outline,
                title: 'New Assessment',
                subtitle: 'Check your health',
                color: const Color(0xFF1976D2),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(builder: (context) => const HomeScreen()),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildEnhancedActionCard(
                icon: Icons.trending_up,
                title: 'View Trends',
                subtitle: 'See your progress',
                color: const Color(0xFF4CAF50),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const PredictionHistoryScreen(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildEnhancedActionCard(
                icon: Icons.local_hospital,
                title: 'Find Hospital',
                subtitle: 'Nearby healthcare',
                color: const Color(0xFFE53935),
                onTap: () => _showComingSoonDialog('Hospital Finder'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildEnhancedActionCard(
                icon: Icons.school,
                title: 'Learn More',
                subtitle: 'Health education',
                color: const Color(0xFFFF9800),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const BeautifulEducationScreen(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEnhancedActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [color, color.withValues(alpha: 0.8)],
                ),
                borderRadius: BorderRadius.circular(14),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(icon, color: Colors.white, size: 28),
            ),
            const SizedBox(height: 14),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreakCard() {
    final streak = _getHealthStreak();
    return DashboardWidgets.buildHealthStreak(
      streakDays: streak,
      message: streak > 7
          ? 'Amazing consistency! You\'re on fire!'
          : 'Keep going! Build your health habit.',
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Recent Activity',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
            if (_recentPredictions.isNotEmpty)
              TextButton.icon(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const PredictionHistoryScreen(),
                    ),
                  );
                },
                icon: const Icon(Icons.arrow_forward, size: 16),
                label: const Text('View All'),
                style: TextButton.styleFrom(
                  foregroundColor: const Color(0xFF1976D2),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),
        if (_recentPredictions.isEmpty)
          _buildEmptyActivityState()
        else
          ..._recentPredictions.take(3).map((prediction) {
            return _buildEnhancedActivityCard(prediction);
          }),
      ],
    );
  }

  Widget _buildEmptyActivityState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFF1976D2).withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.assessment_outlined,
              size: 48,
              color: const Color(0xFF1976D2).withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Ready to start your health journey?',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2C3E50),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Take your first assessment to get personalized health insights',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const HomeScreen()),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1976D2),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
            icon: const Icon(Icons.play_arrow),
            label: const Text('Start Assessment'),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedActivityCard(PredictionResult prediction) {
    final riskColor = _getRiskColor(prediction.riskLevel);
    final riskIcon = _getRiskIcon(prediction.riskLevel);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: riskColor.withValues(alpha: 0.1), width: 1),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [riskColor, riskColor.withValues(alpha: 0.8)],
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(riskIcon, color: Colors.white, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Health Assessment',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Risk Level: ${prediction.riskLevel}',
                  style: TextStyle(
                    fontSize: 13,
                    color: riskColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _formatTimeAgo(prediction.createdAt),
                  style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: riskColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _getScoreFromRisk(prediction.riskLevel),
              style: TextStyle(
                fontSize: 12,
                color: riskColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getRiskColor(String riskLevel) {
    final level = riskLevel.toLowerCase();
    if (level.contains('low')) {
      return const Color(0xFF00C851); // New vibrant green for low risk
    }
    if (level.contains('medium')) {
      return const Color(0xFFFFB300); // New amber for medium risk
    }
    return const Color(0xFFFF3547); // New bright red for high risk
  }

  IconData _getRiskIcon(String riskLevel) {
    final level = riskLevel.toLowerCase();
    if (level.contains('low')) return Icons.check_circle;
    if (level.contains('medium')) return Icons.warning;
    return Icons.error;
  }

  String _getScoreFromRisk(String riskLevel) {
    final level = riskLevel.toLowerCase();
    if (level.contains('low')) return '90+';
    if (level.contains('medium')) return '70+';
    return '40+';
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  Widget _buildEnhancedFAB() {
    return ScaleTransition(
      scale: _pulseAnimation,
      child: FloatingActionButton.extended(
        heroTag: "enhanced_dashboard_fab", // Unique hero tag
        onPressed: () {
          Navigator.of(
            context,
          ).push(MaterialPageRoute(builder: (context) => const HomeScreen()));
        },
        backgroundColor: const Color(0xFF1976D2),
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('New Assessment'),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        elevation: 8,
      ),
    );
  }

  void _showComingSoonDialog(String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.construction, color: Colors.orange[600]),
            const SizedBox(width: 8),
            const Text('Coming Soon'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '$feature feature is under development and will be available soon!',
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info, color: Color(0xFF1976D2), size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'We\'re working hard to bring you the best experience!',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showNotificationsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.notifications, color: Color(0xFF1976D2)),
            SizedBox(width: 8),
            Text('Health Reminders'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildNotificationItem(
              icon: Icons.schedule,
              title: 'Daily Check-in',
              subtitle: 'Remember to track your health daily',
              color: const Color(0xFF4CAF50),
            ),
            const SizedBox(height: 12),
            _buildNotificationItem(
              icon: Icons.local_hospital,
              title: 'Health Tip',
              subtitle: 'Stay hydrated - drink 8 glasses of water',
              color: const Color(0xFF2196F3),
            ),
            const SizedBox(height: 12),
            _buildNotificationItem(
              icon: Icons.trending_up,
              title: 'Progress Update',
              subtitle: 'Your health score improved by 5%',
              color: const Color(0xFFFF9800),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.person, color: Color(0xFF1976D2)),
            SizedBox(width: 8),
            Text('Profile'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: const Color(0xFF1976D2).withValues(alpha: 0.1),
              child: Text(
                _currentUser!.fullName.isNotEmpty
                    ? _currentUser!.fullName[0].toUpperCase()
                    : _currentUser!.username[0].toUpperCase(),
                style: const TextStyle(
                  color: Color(0xFF1976D2),
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _currentUser!.fullName.isNotEmpty
                  ? _currentUser!.fullName
                  : _currentUser!.username,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              _currentUser!.email,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Health Score:'),
                      Text(
                        '${(_getHealthScore() * 100).round()}%',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Total Assessments:'),
                      Text(
                        '${_recentPredictions.length}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.settings, color: Color(0xFF1976D2)),
            SizedBox(width: 8),
            Text('Settings'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSettingItem(
              icon: Icons.notifications,
              title: 'Notifications',
              subtitle: 'Manage your health reminders',
              onTap: () {
                Navigator.of(context).pop();
                _showComingSoonDialog('Notification Settings');
              },
            ),
            const SizedBox(height: 12),
            _buildSettingItem(
              icon: Icons.privacy_tip,
              title: 'Privacy',
              subtitle: 'Data privacy and security',
              onTap: () {
                Navigator.of(context).pop();
                _showComingSoonDialog('Privacy Settings');
              },
            ),
            const SizedBox(height: 12),
            _buildSettingItem(
              icon: Icons.help,
              title: 'Help & Support',
              subtitle: 'Get help and contact support',
              onTap: () {
                Navigator.of(context).pop();
                _showComingSoonDialog('Help & Support');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: const Color(0xFF1976D2), size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 12, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }
}
