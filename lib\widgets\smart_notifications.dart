import 'package:flutter/material.dart';
import '../models/prediction_result.dart';
import '../models/user.dart';

class SmartNotification {
  final String id;
  final String title;
  final String message;
  final IconData icon;
  final Color color;
  final NotificationType type;
  final DateTime createdAt;
  final bool isRead;
  final String? actionText;
  final VoidCallback? onAction;

  SmartNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.icon,
    required this.color,
    required this.type,
    required this.createdAt,
    this.isRead = false,
    this.actionText,
    this.onAction,
  });
}

enum NotificationType {
  reminder,
  achievement,
  healthTip,
  warning,
  celebration,
  motivation,
}

class SmartNotificationSystem {
  static List<SmartNotification> generateNotifications(
    User user,
    List<PredictionResult> predictions,
  ) {
    final notifications = <SmartNotification>[];
    final now = DateTime.now();

    // Daily reminder notifications
    if (_shouldShowDailyReminder(predictions)) {
      notifications.add(
        SmartNotification(
          id: 'daily_reminder_${now.millisecondsSinceEpoch}',
          title: 'Daily Health Check',
          message:
              'Don\'t forget to track your health today! Consistency is key to better health outcomes.',
          icon: Icons.schedule,
          color: const Color(0xFF2196F3),
          type: NotificationType.reminder,
          createdAt: now,
          actionText: 'Take Assessment',
        ),
      );
    }

    // Health tips based on recent assessments
    if (predictions.isNotEmpty) {
      final latestRisk = predictions.first.riskLevel.toLowerCase();
      notifications.add(_generateHealthTip(latestRisk, now));
    }

    // Achievement notifications
    notifications.addAll(_generateAchievementNotifications(predictions, now));

    // Motivational notifications
    notifications.add(
      _generateMotivationalNotification(user, predictions, now),
    );

    // Warning notifications for high risk
    if (predictions.isNotEmpty &&
        predictions.first.riskLevel.toLowerCase().contains('high')) {
      notifications.add(
        SmartNotification(
          id: 'high_risk_warning_${now.millisecondsSinceEpoch}',
          title: 'Health Alert',
          message:
              'Your recent assessment shows higher risk. Consider consulting a healthcare professional.',
          icon: Icons.warning,
          color: const Color(0xFFE53935),
          type: NotificationType.warning,
          createdAt: now,
          actionText: 'Find Hospital',
        ),
      );
    }

    // Celebration notifications for improvements
    if (_hasRecentImprovement(predictions)) {
      notifications.add(
        SmartNotification(
          id: 'improvement_celebration_${now.millisecondsSinceEpoch}',
          title: 'Great Progress! 🎉',
          message:
              'Your health score has improved! Keep up the excellent work.',
          icon: Icons.celebration,
          color: const Color(0xFF4CAF50),
          type: NotificationType.celebration,
          createdAt: now,
        ),
      );
    }

    return notifications;
  }

  static bool _shouldShowDailyReminder(List<PredictionResult> predictions) {
    if (predictions.isEmpty) return true;

    final lastAssessment = predictions.first.createdAt;
    final hoursSinceLastAssessment = DateTime.now()
        .difference(lastAssessment)
        .inHours;

    return hoursSinceLastAssessment >= 24;
  }

  static SmartNotification _generateHealthTip(String riskLevel, DateTime now) {
    if (riskLevel.contains('low')) {
      return SmartNotification(
        id: 'health_tip_low_${now.millisecondsSinceEpoch}',
        title: 'Health Tip 💡',
        message:
            'Great job maintaining low risk! Remember to stay hydrated and get regular exercise.',
        icon: Icons.lightbulb,
        color: const Color(0xFF4CAF50),
        type: NotificationType.healthTip,
        createdAt: now,
      );
    } else if (riskLevel.contains('medium')) {
      return SmartNotification(
        id: 'health_tip_medium_${now.millisecondsSinceEpoch}',
        title: 'Health Tip 💡',
        message:
            'Consider lifestyle improvements: balanced diet, regular sleep, and stress management.',
        icon: Icons.lightbulb,
        color: const Color(0xFFFF9800),
        type: NotificationType.healthTip,
        createdAt: now,
      );
    } else {
      return SmartNotification(
        id: 'health_tip_high_${now.millisecondsSinceEpoch}',
        title: 'Health Tip 💡',
        message:
            'Focus on immediate health improvements and consider professional medical advice.',
        icon: Icons.lightbulb,
        color: const Color(0xFFE53935),
        type: NotificationType.healthTip,
        createdAt: now,
      );
    }
  }

  static List<SmartNotification> _generateAchievementNotifications(
    List<PredictionResult> predictions,
    DateTime now,
  ) {
    final notifications = <SmartNotification>[];
    final assessmentCount = predictions.length;

    // First assessment achievement
    if (assessmentCount == 1) {
      notifications.add(
        SmartNotification(
          id: 'achievement_first_${now.millisecondsSinceEpoch}',
          title: 'Achievement Unlocked! 🏆',
          message:
              'Health Explorer - You completed your first health assessment!',
          icon: Icons.emoji_events,
          color: const Color(0xFFFFD700),
          type: NotificationType.achievement,
          createdAt: now,
        ),
      );
    }

    // Milestone achievements
    if (assessmentCount == 5) {
      notifications.add(
        SmartNotification(
          id: 'achievement_five_${now.millisecondsSinceEpoch}',
          title: 'Achievement Unlocked! 🏆',
          message: 'Health Tracker - You\'ve completed 5 health assessments!',
          icon: Icons.emoji_events,
          color: const Color(0xFFFFD700),
          type: NotificationType.achievement,
          createdAt: now,
        ),
      );
    }

    return notifications;
  }

  static SmartNotification _generateMotivationalNotification(
    User user,
    List<PredictionResult> predictions,
    DateTime now,
  ) {
    final motivationalMessages = [
      'Your health journey matters. Every assessment brings you closer to better health!',
      'Consistency is key! Keep tracking your health regularly.',
      'You\'re taking control of your health - that\'s something to be proud of!',
      'Small steps lead to big changes. Keep going!',
      'Your future self will thank you for taking care of your health today.',
    ];

    final randomMessage =
        motivationalMessages[now.millisecondsSinceEpoch %
            motivationalMessages.length];

    return SmartNotification(
      id: 'motivation_${now.millisecondsSinceEpoch}',
      title: 'Stay Motivated! 💪',
      message: randomMessage,
      icon: Icons.favorite,
      color: const Color(0xFFE91E63),
      type: NotificationType.motivation,
      createdAt: now,
    );
  }

  static bool _hasRecentImprovement(List<PredictionResult> predictions) {
    if (predictions.length < 2) return false;

    final latest = predictions.first.riskLevel.toLowerCase();
    final previous = predictions[1].riskLevel.toLowerCase();

    final latestScore = _getRiskScore(latest);
    final previousScore = _getRiskScore(previous);

    return latestScore > previousScore;
  }

  static int _getRiskScore(String riskLevel) {
    if (riskLevel.contains('low')) return 3;
    if (riskLevel.contains('medium')) return 2;
    return 1;
  }
}

class SmartNotificationWidget extends StatefulWidget {
  final User user;
  final List<PredictionResult> predictions;

  const SmartNotificationWidget({
    super.key,
    required this.user,
    required this.predictions,
  });

  @override
  State<SmartNotificationWidget> createState() =>
      _SmartNotificationWidgetState();
}

class _SmartNotificationWidgetState extends State<SmartNotificationWidget> {
  late List<SmartNotification> notifications;

  @override
  void initState() {
    super.initState();
    notifications = SmartNotificationSystem.generateNotifications(
      widget.user,
      widget.predictions,
    );
  }

  @override
  Widget build(BuildContext context) {
    final recentNotifications = notifications.take(3).toList();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.notifications_active,
                color: Color(0xFF1976D2),
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                'Smart Notifications',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
              const Spacer(),
              if (notifications.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1976D2).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${notifications.length}',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          if (recentNotifications.isEmpty)
            _buildEmptyNotifications()
          else
            Column(
              children: recentNotifications.map((notification) {
                return _buildNotificationItem(notification);
              }).toList(),
            ),
          if (notifications.length > 3) ...[
            const SizedBox(height: 12),
            GestureDetector(
              onTap: () => _showAllNotifications(),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF1976D2).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.view_list, color: Color(0xFF1976D2), size: 16),
                    SizedBox(width: 8),
                    Text(
                      'View All Notifications',
                      style: TextStyle(
                        color: Color(0xFF1976D2),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyNotifications() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(Icons.notifications_none, size: 32, color: Colors.grey[400]),
          const SizedBox(height: 8),
          Text(
            'No new notifications',
            style: TextStyle(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'We\'ll notify you about important health updates',
            style: TextStyle(color: Colors.grey[500], fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationItem(SmartNotification notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: notification.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: notification.color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: notification.color,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(notification.icon, color: Colors.white, size: 14),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  notification.title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2C3E50),
                  ),
                ),
              ),
              Text(
                _formatTime(notification.createdAt),
                style: TextStyle(fontSize: 10, color: Colors.grey[500]),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            notification.message,
            style: TextStyle(fontSize: 12, color: Colors.grey[700]),
          ),
          if (notification.actionText != null) ...[
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: notification.onAction,
                style: TextButton.styleFrom(
                  foregroundColor: notification.color,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  notification.actionText!,
                  style: const TextStyle(fontSize: 12),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else {
      return '${difference.inDays}d';
    }
  }

  void _showAllNotifications() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'All Notifications',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    itemCount: notifications.length,
                    itemBuilder: (context, index) {
                      return _buildNotificationItem(notifications[index]);
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
