# Android Studio Installation Guide

## Method 2: Using Android Studio

### Step 1: Open Project in Android Studio
1. Open Android Studio
2. Click "Open an existing Android Studio project"
3. Navigate to: `C:\Users\<USER>\Desktop\hiv_predictor_app\android`
4. Click "OK"

### Step 2: Connect Device
1. Connect your Infinix X6517 via USB
2. Enable USB Debugging (already done)
3. In Android Studio, you should see your device in the device dropdown

### Step 3: Run the App
1. Click the green "Run" button (▶️)
2. Select your Infinix X6517 device
3. Android Studio will build and install the app

## Method 3: ADB Direct Installation

### Prerequisites
- ADB (Android Debug Bridge) installed
- Device connected with USB debugging enabled

### Commands
```bash
# Check if device is detected
adb devices

# Install APK directly
adb install build\app\outputs\flutter-apk\app-debug.apk

# Launch the app
adb shell am start -n com.example.hiv_predictor_app/.MainActivity
```

## Method 4: Wireless Debugging (Android 11+)

### Step 1: Enable Wireless Debugging
1. Go to Settings > Developer Options
2. Enable "Wireless debugging"
3. Tap "Pair device with pairing code"

### Step 2: Connect via WiFi
```bash
# Pair with device (use IP and port from phone)
adb pair <IP>:<PORT>

# Connect to device
adb connect <IP>:<PORT>

# Now you can use flutter run wirelessly
flutter run
```
